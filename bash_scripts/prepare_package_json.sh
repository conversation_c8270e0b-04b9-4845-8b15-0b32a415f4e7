#!/bin/bash

PACKAGE_JSON="./package.json"

# Verifica se o arquivo package.json existe
if [[ ! -f "$PACKAGE_JSON" ]]; then
  echo "Arquivo package.json não encontrado!"
  exit 1
fi

# Remove a devDependency 'husky' do package.json
echo "Removendo husky das devDependencies..."
jq 'del(.devDependencies.husky)' "$PACKAGE_JSON" > "$PACKAGE_JSON.tmp" && mv "$PACKAGE_JSON.tmp" "$PACKAGE_JSON"

# Remove o script postinstall do package.json
echo "Removendo o script postinstall..."
jq 'del(.scripts.postinstall)' "$PACKAGE_JSON" > "$PACKAGE_JSON.tmp" && mv "$PACKAGE_JSON.tmp" "$PACKAGE_JSON"

# Remove o script prepare do package.json
echo "Removendo o script postinstall..."
jq 'del(.scripts.prepare)' "$PACKAGE_JSON" > "$PACKAGE_JSON.tmp" && mv "$PACKAGE_JSON.tmp" "$PACKAGE_JSON"

echo "Dependência husky e os scripts postinstall, prepare foram removidos com sucesso!"
