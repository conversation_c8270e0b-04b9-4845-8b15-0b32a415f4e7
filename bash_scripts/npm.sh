#!/bin/bash

# Validar se foram fornecidos dois parâmetros
if [ "$#" -ne 2 ]; then
  echo "Uso: $0 <node_version> <npm_command>"
  echo "Exemplo: $0 14.17.0 install"
  exit 1
fi

# Carregar o NVM
export NVM_DIR="$HOME/.nvm"
if [ -s "$NVM_DIR/nvm.sh" ]; then
  # Carregar NVM
  source "$NVM_DIR/nvm.sh"
else
  echo "NVM não encontrado. Verifique a instalação."
  exit 1
fi

# Usar a versão especificada do Node.js
nvm use "$1"
if [ $? -ne 0 ]; then
  echo "Versão do Node.js '$1' não encontrada. Instalando..."
  nvm install "$1"
  if [ $? -ne 0 ]; then
    echo "Não foi possível instalar a versão do Node.js: $1"
    exit 1
  fi

  # Tentar usar a versão instalada
  nvm use "$1"
  if [ $? -ne 0 ]; then
    echo "Não foi possível usar a versão do Node.js recém-instalada: $1"
    exit 1
  fi
fi

# Executar o comando npm fornecido
if [[ $2 == i* ]]; then
    npm $2
else
    npm run $2
fi

if [ $? -ne 0 ]; then
  echo "O comando npm '$2' falhou."
  exit 1
fi
