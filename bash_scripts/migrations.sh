#!/bin/bash

# Validar se foram fornecidos dois parâmetros
if [ "$#" -ne 2 ]; then
  echo "Uso: $0 <node_version> <nome_do_diretorio>"
  echo "Exemplo: $0 14.17.0 homologation"
  exit 1
fi

SCRIPT_DIR="$(dirname "$0")"
source "$SCRIPT_DIR/load_secret_env.sh" "$2"

# Carregar o NVM
export NVM_DIR="$HOME/.nvm"
if [ -s "$NVM_DIR/nvm.sh" ]; then
  # Carregar NVM
  source "$NVM_DIR/nvm.sh"
else
  echo "NVM não encontrado. Verifique a instalação."
  exit 1
fi

# Usar a versão especificada do Node.js
nvm install 20.16.0
nvm use 20.16.0
npm install -g migrate-mongo
npm install -g mongodb

if [ $? -ne 0 ]; then
  echo "Não foi possível usar a versão do Node.js especificada: $1"
  exit 1
fi

# Diretório de migrações baseado no parâmetro
MIGRATIONS_DIR="migrations/$2"

# Verifica se o diretório existe
if [ ! -d "$MIGRATIONS_DIR" ]; then
  echo "Erro: O diretório '$MIGRATIONS_DIR' não existe."
  exit 1
fi

# Lista todas as pastas dentro de migrations/<parametro>
for dir in "$MIGRATIONS_DIR"/*/; do
  if [ -d "$dir" ]; then
    echo "Entrando no diretório: $dir"

    # Navega até a pasta
    cd "$dir" || { echo "Erro ao acessar o diretório $dir"; exit 1; }

    # Executa o comando 'migrate-mongo up'
    echo "Executando 'migrate-mongo up' em $(pwd)"
    npx migrate-mongo up

    if [ $? -ne 0 ]; then
      echo "Erro ao executar 'migrate-mongo up' em $(pwd)"
      exit 1
    fi

    # Volta para o diretório anterior
    cd - > /dev/null
  fi
done

echo "Script concluído!"
