#!/bin/bash

# Verificar se o nome da VM foi passado como parâmetro
if [ -z "$1" ]; then
  echo "Erro: Nome da VM não foi fornecido."
  echo "Uso: $0 <nome_da_vm>"
  exit 1
fi

# Atribuir o parâmetro de entrada à variável vm_name
vm_name=$1

# Determinar root de acordo com o nome da VM
case "$vm_name" in
  "piaget-public-api"|"piaget-public-api-2"|"api-gateway-auth"|"api-gateway-auth2")
    echo "true"
    ;;
  *)
    echo "false"
    ;;
esac
