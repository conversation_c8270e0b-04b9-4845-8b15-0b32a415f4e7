#!/bin/bash

# Verificar se foi passado pelo menos um parâmetro
if [ "$#" -lt 1 ]; then
  echo "Uso: $0 <comando_pm2>"
  echo "Exemplo: $0 restart all"
  exit 1
fi

# Carregar o NVM
export NVM_DIR="$HOME/.nvm"
if [ -s "$NVM_DIR/nvm.sh" ]; then
  # Carregar NVM
  source "$NVM_DIR/nvm.sh"
else
  echo "NVM não encontrado. Verifique a instalação."
  exit 1
fi

# Instalar e usar Node.js versão 20
nvm install 20.16.0
nvm use 20.16.0

# Verificar se o pm2 está instalado globalmente, senão, instalar
if ! command -v pm2 &> /dev/null; then
  echo "PM2 não encontrado, instalando globalmente..."
  npm install -g pm2
fi

# Executar o comando pm2 com o parâmetro recebido
pm2 $1

# Verificar se o comando pm2 foi executado corretamente e falhar o script se necessário
if [ $? -ne 0 ]; then
  exit 1
fi
