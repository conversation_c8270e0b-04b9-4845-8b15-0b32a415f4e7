#!/bin/bash

# Validar se foram fornecidos dois parâmetros
if [ "$#" -ne 2 ]; then
  echo "Uso: $0 <node_version> <nome pasta atual>"
  echo "Exemplo: $0 14.17.0"
  exit 1
fi

SCRIPT_DIR=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)
CACHE_DIR="/tmp/cache_node_modules/$2"
CACHED_HASH=$(cat $CACHE_DIR/.hash 2>/dev/null)
HASH="$(sha256sum package.json | cut -d ' ' -f 1)"

if [ "$CACHED_HASH" == "$HASH" ]; then
  echo "Usando o cache de node_modules..."
  cp -R $CACHE_DIR/node_modules ./
else
  bash "$SCRIPT_DIR/npm.sh" "$1" i
  mkdir -p "$CACHE_DIR"
  cp -R node_modules $CACHE_DIR
  echo "$HASH" > $CACHE_DIR/.hash
fi
