#!/bin/bash

# Script para buscar segredos do Google Cloud Secret Manager e
# exportá-los como variáveis de ambiente ou gerar um formato .env.
#
# Uso:
#   ./fetch_secrets.sh <ambiente>       # Exporta variáveis para o shell atual
#   ./fetch_secrets.sh <ambiente> --env # Imprime variáveis no formato .env para stdout
#
# Argumentos:
#   <ambiente>: 'homologation' ou 'production'. Define o prefixo do segredo.
#   --env     : Flag opcional para mudar o formato de saída para .env.

# --- Configuração Inicial e Validação de Argumentos ---

ENVIRONMENT=""
OUTPUT_FORMAT="export" # Padrão é exportar

# Processa os argumentos de forma mais flexível
while [[ $# -gt 0 ]]; do
  key="$1"
  case $key in
    --env)
      OUTPUT_FORMAT="dotenv"
      shift # Passa argumento
      ;;
    homologation|production)
      # Se ENVIRONMENT já foi setado, é um erro (passou duas vezes)
      if [[ -n "$ENVIRONMENT" ]]; then
          echo "Erro: Ambiente especificado mais de uma vez." >&2
          exit 1
      fi
      ENVIRONMENT="$1"
      shift # Passa argumento
      ;;
    *)
      # Argumento desconhecido
      echo "Erro: Argumento inválido '$1'." >&2
      echo "Uso: $0 <homologation|production> [--env]" >&2
      exit 1
      ;;
  esac
done

# Verifica se o ambiente foi fornecido
if [[ -z "$ENVIRONMENT" ]]; then
  echo "Erro: Ambiente ('homologation' or 'production') não fornecido." >&2
  echo "Uso: $0 <homologation|production> [--env]" >&2
  exit 1
fi

# --- Lógica Principal ---

# Define o prefixo do segredo com base no ambiente
SECRET_PREFIX="mongodb-${ENVIRONMENT}-"

# Lista segredos que correspondem ao prefixo. Redireciona stderr para stdout em caso de erro no gcloud.
SECRETS_OUTPUT=$(gcloud secrets list --filter="name ~ ${SECRET_PREFIX}" --format="value(name)" 2>&1)
GCLOUD_LIST_STATUS=$? # Captura o status de saída do comando gcloud

# Verifica se o comando gcloud foi executado com sucesso
if [[ $GCLOUD_LIST_STATUS -ne 0 ]]; then
    echo "Erro ao listar segredos do gcloud:" >&2
    echo "$SECRETS_OUTPUT" >&2 # Mostra a saída de erro do gcloud
    exit 1
fi

# Verifica se algum segredo foi encontrado
if [[ -z "$SECRETS_OUTPUT" ]]; then
  echo "Nenhum segredo encontrado com o prefixo: ${SECRET_PREFIX}" >&2
  exit 0 # Sai com sucesso, pois não encontrar segredos não é um erro necessariamente
fi

# Processa cada segredo encontrado
# Use 'while read' para lidar corretamente com nomes de segredos que possam conter espaços (embora improvável aqui)
while IFS= read -r SECRET_NAME; do
  # Pula linhas vazias que podem surgir
  [[ -z "$SECRET_NAME" ]] && continue

  # Recupera o valor mais recente do segredo
  # Redireciona stderr para stdout em caso de erro no gcloud access
  SECRET_VALUE=$(gcloud secrets versions access latest --secret="$SECRET_NAME" 2>&1)
  GCLOUD_ACCESS_STATUS=$?

  # Verifica erro ao acessar o segredo
  if [[ $GCLOUD_ACCESS_STATUS -ne 0 ]]; then
    echo "Erro ao acessar o segredo '$SECRET_NAME':" >&2
    echo "$SECRET_VALUE" >&2 # Mostra a saída de erro do gcloud
    exit 1 # Interrompe o script em caso de falha ao acessar um segredo
  fi

  # Deriva o nome da variável de ambiente a partir do nome do segredo
  # 1. Remove o prefixo
  # 2. Substitui hífens por underscores
  # 3. Converte para maiúsculas
  ENV_VAR_NAME=$(echo "$SECRET_NAME" | sed "s/^${SECRET_PREFIX}//" | tr '-' '_' | awk '{print toupper($0)}')

  # Compacta o valor JSON em uma única linha (necessário para .env e útil para export)
  # Redireciona stderr para stdout em caso de erro no jq
  SECRET_VALUE_ONELINE=$(echo "$SECRET_VALUE" | jq -c '.' 2>&1)
  JQ_STATUS=$?

  # Verifica erro no processamento do JSON com jq
  if [[ $JQ_STATUS -ne 0 ]]; then
     echo "Erro ao processar JSON para o segredo '$SECRET_NAME'. O valor pode não ser JSON válido:" >&2
     echo "Valor recebido: $SECRET_VALUE" >&2
     echo "Erro JQ: $SECRET_VALUE_ONELINE" >&2 # Mostra a saída de erro do jq
     exit 1 # Interrompe o script se o valor não for JSON válido
  fi

  # --- Saída ---
  if [[ "$OUTPUT_FORMAT" == "dotenv" ]]; then
    # Formato .env: VAR_NAME="valor_escapado"
    # Escapa aspas duplas dentro do valor JSON compactado
    ESCAPED_VALUE=$(echo "$SECRET_VALUE_ONELINE" | sed 's/"/\\"/g')
    # Usa printf para garantir a formatação correta e a quebra de linha real
    printf '%s="%s"\n' "$ENV_VAR_NAME" "$ESCAPED_VALUE"
  else
    # Formato export: exporta diretamente a variável
    export "$ENV_VAR_NAME"="$SECRET_VALUE_ONELINE"
    echo "Variável de ambiente carregada: $ENV_VAR_NAME" >&2 # Mensagem informativa para stderr
  fi

done <<< "$SECRETS_OUTPUT" # Alimenta o loop while com a lista de nomes de segredos

exit 0
