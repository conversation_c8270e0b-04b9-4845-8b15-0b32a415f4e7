{"name": "microservices_vestibular", "version": "1.3.0", "description": "Basic strcuture for REST API with ExpressJS", "main": "src/app.js", "target": {"node": "20.16.0"}, "scripts": {"dev:prominas": "export WORKSPACE='prominas' && export NODE_ENV='development' && nodemon --exec babel-node src/app.js --use_strict", "build:prominas": "export WORKSPACE='prominas' && export NODE_ENV='production' && npm run clean && babel src --ignore test.js --out-dir ./dist --copy-files && npm run migrate:production", "clean": "rm -rf dist && mkdir dist", "start:prominas": "pm2 start pm2-deploy-prominas.yml", "stop:prominas": "pm2 stop pm2-deploy-prominas.yml", "restart:prominas": "pm2 restart pm2-deploy-prominas.yml", "build-homo:prominas": "export WORKSPACE='prominas' && export NODE_ENV='homologation' && npm run clean-homo && babel src --ignore test.js --out-dir ./dist-homo --copy-files && npm run migrate:homologation", "clean-homo": "rm -rf dist-homo && mkdir dist-homo", "start-homo:prominas": "pm2 start pm2-deploy-prominas-homo.yml", "stop-homo:prominas": "pm2 stop pm2-deploy-prominas-homo.yml", "restart-homo:prominas": "pm2 restart pm2-deploy-prominas-homo.yml", "start-docker": "sudo docker-compose up -d ; sudo docker ps", "stop-docker": "sudo docker-compose stop", "update": "sh update.sh", "test-build-homo:prominas": "export WORKSPACE='prominas' && export NODE_ENV='homologation' && node dist-homo/app.js --test-deploy", "test-build-prod:prominas": "export WORKSPACE='prominas' && export NODE_ENV='production' && node dist/app.js --test-deploy", "secret-env:homologation": "bash bash_scripts/load_secret_env.sh homologation", "secret-env:production": "bash bash_scripts/load_secret_env.sh production", "prepare": "husky", "backup-precommit": "cp .husky/pre-commit .husky/pre-commit.bak", "restore-precommit": "cp .husky/pre-commit.bak .husky/pre-commit && rm .husky/pre-commit.bak", "postinstall": "npm run backup-precommit && npx husky init && npm run restore-precommit", "migrate:homologation": "bash bash_scripts/migrations.sh 20.16.0 homologation", "migrate:production": "bash bash_scripts/migrations.sh 20.16.0 production", "backup-husky": "cp -r .husky .husky_bkp", "restore-husky": "rm -rf .husky && mv .husky_bkp .husky", "sonar": "npx sonarqube-scanner -Dsonar.projectKey=$(basename $(pwd)) -Dsonar.token=sqa_a8e606f6ea58bc9aafc226fad5dbba4b78ee1f05", "sonar:changed": "CHANGED_FILES=$(git diff --cached --name-only | tr '\\n' ',' | sed 's/,$//' ) && echo \"Analyzing files: $CHANGED_FILES\" && npx sonarqube-scanner -Dsonar.projectKey=$(basename $(pwd)) -Dsonar.token=sqa_a8e606f6ea58bc9aafc226fad5dbba4b78ee1f05 -Dsonar.inclusions=\"$CHANGED_FILES\"", "sonar:check": "curl -s -u \"sqa_a8e606f6ea58bc9aafc226fad5dbba4b78ee1f05:\" \"https://sonar.institutoprominas.com.br/api/qualitygates/project_status?projectKey=$(basename $(pwd))\" | grep -o '\\\"status\\\":\\\"[^\\\"]*\\\"' | cut -d'\\\"' -f4"}, "author": "Tales Luna <<EMAIL>>", "license": "MIT", "husky": {"skipCI": true, "hooks": {"pre-commit": "npx lint-staged"}}, "lint-staged": {"*.{js,jsx,ts,tsx}": "eslint --fix"}, "devDependencies": {"@babel/cli": "^7.24.8", "@babel/core": "^7.25.2", "@babel/node": "^7.25.0", "@babel/plugin-proposal-optional-chaining": "^7.6.0", "@babel/plugin-transform-parameters": "^7.24.7", "@babel/plugin-transform-unicode-sets-regex": "^7.24.7", "@babel/preset-env": "^7.25.3", "@babel/register": "^7.24.6", "@eslint/js": "^9.8.0", "@types/mongoose": "^5.11.97", "babel-preset-minify": "0.5.2", "eslint": "^9.29.0", "globals": "^15.9.0", "husky": "^9.1.6", "lint-staged": "^15.2.7", "nodemon": "^3.1.4", "npm-check": "^6.0.1", "supertest": "^7.0.0", "@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0"}, "dependencies": {"@babel/polyfill": "^7.12.1", "bluebird": "^3.7.2", "body-parser": "^1.20.2", "compression": "^1.7.4", "dotenv": "^16.4.5", "ejs": "^3.1.10", "eslint-plugin-promise": "^7.0.0", "exceljs": "~4.4.0", "express": "^4.19.2", "express-http-context": "^1.2.4", "helmet": "^7.1.0", "http-status-codes": "^2.3.0", "joi": "^10.5.2", "joi-i18n": "^13.1.4", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "moment": "^2.30.1", "mongoose": "^8.5.2", "morgan": "^1.10.0", "node-fetch": "^3.3.2", "nodemailer": "^6.9.14", "request": "^2.88.2", "request-ip": "^3.3.0", "request-promise": "^4.2.6", "sequelize": "^6.37.3"}, "engines": {"node": "20.16.0"}}