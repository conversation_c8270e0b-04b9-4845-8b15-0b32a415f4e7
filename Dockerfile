FROM node:alpine

# Set -3 UTC time
RUN apk update
RUN apk add tzdata
RUN cp /usr/share/zoneinfo/America/Sao_Paulo /etc/localtime
RUN echo "America/Sao_Paulo" >  /etc/timezone
RUN date

# Create app directory
RUN mkdir -p /usr/src/service
WORKDIR /usr/src/service

# Here go all logs
RUN mkdir -p storage/logs/app
RUN chmod 777 storage/logs/app

# Install dependencies
RUN npm install -g pm2

# Start service
EXPOSE 3001
CMD [ "sh" ]
