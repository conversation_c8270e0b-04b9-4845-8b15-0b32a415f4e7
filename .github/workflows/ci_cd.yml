name: CI/CD Analysis

on:
  push:
    branches:
      - 'DES-*'
      - 'CDL-*'
      - 'MOBILE-*'
  pull_request:
    branches:
      - master
      - homologation
      - main

jobs:
  lint:
    name: Run Lint
    runs-on: ubuntu-latest
    if: github.event_name == 'push' || (github.event_name == 'pull_request' && (github.base_ref == 'refs/heads/master' || github.base_ref == 'refs/heads/homologation' || github.base_ref == 'refs/heads/main'))
    strategy:
      fail-fast: true

    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Check for bypass permissions
        id: bypass-check
        run: |
          # Check if user has admin permissions or bypass permissions
          USER_PERMISSIONS=$(curl -s -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}" \
            "https://api.github.com/repos/${{ github.repository }}/collaborators/${{ github.actor }}/permission")

          PERMISSION_LEVEL=$(echo "$USER_PERMISSIONS" | jq -r '.permission')

          # Check if user has admin permissions
          if [[ "$PERMISSION_LEVEL" == "admin" ]]; then
            echo "bypass=true" >> $GITHUB_OUTPUT
            echo "Bypass allowed: User ${{ github.actor }} has admin permissions."
          else
            # Check if user can bypass branch protection (requires admin or specific bypass permissions)
            BRANCH_PROTECTION=$(curl -s -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}" \
              "https://api.github.com/repos/${{ github.repository }}/branches/${{ github.ref_name }}/protection" 2>/dev/null || echo "{}")

            # If branch protection exists, check if user can bypass it
            if [[ "$BRANCH_PROTECTION" != "{}" ]]; then
              USER_CAN_BYPASS=$(curl -s -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}" \
                "https://api.github.com/repos/${{ github.repository }}/branches/${{ github.ref_name }}/protection/restrictions/users" 2>/dev/null | \
                jq -r --arg user "${{ github.actor }}" '.[] | select(.login == $user) | .login' || echo "")

              if [[ -n "$USER_CAN_BYPASS" ]] || [[ "$PERMISSION_LEVEL" == "admin" ]]; then
                echo "bypass=true" >> $GITHUB_OUTPUT
                echo "Bypass allowed: User ${{ github.actor }} has bypass permissions."
              else
                echo "bypass=false" >> $GITHUB_OUTPUT
                echo "Bypass denied: User ${{ github.actor }} does not have bypass permissions."
              fi
            else
              echo "bypass=false" >> $GITHUB_OUTPUT
              echo "Bypass denied: User ${{ github.actor }} does not have admin permissions."
            fi
          fi

      - name: Set up Node.js
        if: steps.bypass-check.outputs.bypass == 'false'
        uses: actions/setup-node@v4
        with:
          node-version: '20.16.0'

      - name: Install ESLint dependencies
        if: steps.bypass-check.outputs.bypass == 'false'
        run: rm -f package-lock.json package.json && npm install --no-package-lock --no-save eslint @eslint/js globals

      - name: Run eslint on changed files
        id: eslint
        if: steps.bypass-check.outputs.bypass == 'false'
        run: |
          if [ "${{ github.event_name }}" == "pull_request" ]; then
            # For pull requests, get files changed between base and head
            CHANGED_FILES_RAW=$(git diff --name-only ${{ github.event.pull_request.base.sha }} ${{ github.event.pull_request.head.sha }} | grep -E '\.(js|jsx|ts|tsx)$')
          else
            # For pushes, get files changed compared to master branch
            git fetch origin master:master 2>/dev/null || true
            CHANGED_FILES_RAW=$(git diff --name-only master...HEAD | grep -E '\.(js|jsx|ts|tsx)$')
          fi

          # Filter out non-existent files
          CHANGED_FILES=""
          for file in $CHANGED_FILES_RAW; do
            if [ -f "$file" ]; then
              CHANGED_FILES="$CHANGED_FILES $file"
            fi
          done
          CHANGED_FILES=$(echo "$CHANGED_FILES" | xargs)

          if [ -n "$CHANGED_FILES" ]; then
            echo "Changed files: $CHANGED_FILES"
            npx eslint $CHANGED_FILES
          else
            echo "No JavaScript/TypeScript files changed, skipping eslint"
            exit 0
          fi

      - name: Bypass lint success
        if: steps.bypass-check.outputs.bypass == 'true'
        run: |
          echo "Lint verification bypassed successfully."
      - name: Trigger n8n webhook on eslint failure
        if: ${{ failure() && steps.eslint.outcome == 'failure' }}
        run: |
          commit_message=$(git log -1 --pretty=%B | tr -d '\n' | sed 's/"/\\"/g')
          auth_token=$(echo -n "github:github" | base64)
          logs_url="https://github.com/${GITHUB_REPOSITORY}/actions/runs/${GITHUB_RUN_ID}"

          curl -X POST https://n8n.institutoprominas.com.br/webhook/d10b23b8-f337-499f-8382-20d645f62376 \
            -H 'Content-Type: application/json' \
            -H "Authorization: Basic ${auth_token}" \
            -d "{\"status\":\"failed\", \"message\":\"Linting process failed!\", \"commit_message\":\"${commit_message}\", \"logs_url\":\"${logs_url}\"}"
  sonar:
    name: Run SonarQube Scanner
    runs-on: ubuntu-latest
    if: github.event_name == 'push' || (github.event_name == 'pull_request' && (github.base_ref == 'refs/heads/master' || github.base_ref == 'refs/heads/homologation' || github.base_ref == 'refs/heads/main'))
    strategy:
      fail-fast: true

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0  # Shallow clones should be disabled for a better relevancy of analysis

      - name: Check for bypass permissions
        id: bypass-check
        run: |
          # Check if user has admin permissions or bypass permissions
          USER_PERMISSIONS=$(curl -s -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}" \
            "https://api.github.com/repos/${{ github.repository }}/collaborators/${{ github.actor }}/permission")

          PERMISSION_LEVEL=$(echo "$USER_PERMISSIONS" | jq -r '.permission')

          # Check if user has admin permissions
          if [[ "$PERMISSION_LEVEL" == "admin" ]]; then
            echo "bypass=true" >> $GITHUB_OUTPUT
            echo "Bypass allowed: User ${{ github.actor }} has admin permissions."
          else
            # Check if user can bypass branch protection (requires admin or specific bypass permissions)
            BRANCH_PROTECTION=$(curl -s -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}" \
              "https://api.github.com/repos/${{ github.repository }}/branches/${{ github.ref_name }}/protection" 2>/dev/null || echo "{}")

            # If branch protection exists, check if user can bypass it
            if [[ "$BRANCH_PROTECTION" != "{}" ]]; then
              USER_CAN_BYPASS=$(curl -s -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}" \
                "https://api.github.com/repos/${{ github.repository }}/branches/${{ github.ref_name }}/protection/restrictions/users" 2>/dev/null | \
                jq -r --arg user "${{ github.actor }}" '.[] | select(.login == $user) | .login' || echo "")

              if [[ -n "$USER_CAN_BYPASS" ]] || [[ "$PERMISSION_LEVEL" == "admin" ]]; then
                echo "bypass=true" >> $GITHUB_OUTPUT
                echo "Bypass allowed: User ${{ github.actor }} has bypass permissions."
              else
                echo "bypass=false" >> $GITHUB_OUTPUT
                echo "Bypass denied: User ${{ github.actor }} does not have bypass permissions."
              fi
            else
              echo "bypass=false" >> $GITHUB_OUTPUT
              echo "Bypass denied: User ${{ github.actor }} does not have admin permissions."
            fi
          fi

      - name: Get changed files
        id: changed-files
        if: steps.bypass-check.outputs.bypass == 'false'
        run: |
          if [ "${{ github.event_name }}" == "pull_request" ]; then
            # For pull requests, get files changed between base and head
            CHANGED_FILES_RAW=$(git diff --name-only ${{ github.event.pull_request.base.sha }} ${{ github.event.pull_request.head.sha }})
          else
            # For pushes, get files changed compared to master branch
            git fetch origin master:master 2>/dev/null || true
            CHANGED_FILES_RAW=$(git diff --name-only master...HEAD)
          fi

          # Filter out non-existent files
          CHANGED_FILES_FILTERED=""
          for file in $CHANGED_FILES_RAW; do
            if [ -f "$file" ]; then
              if [ -z "$CHANGED_FILES_FILTERED" ]; then
                CHANGED_FILES_FILTERED="$file"
              else
                CHANGED_FILES_FILTERED="$CHANGED_FILES_FILTERED,$file"
              fi
            fi
          done
          CHANGED_FILES="$CHANGED_FILES_FILTERED"

          echo "Changed files: $CHANGED_FILES"
          echo "files=$CHANGED_FILES" >> $GITHUB_OUTPUT
          echo "has_changes=$([ -n "$CHANGED_FILES" ] && echo 'true' || echo 'false')" >> $GITHUB_OUTPUT

      - name: SonarQube Scan with changed files
        if: steps.bypass-check.outputs.bypass == 'false' && steps.changed-files.outputs.has_changes == 'true'
        uses: SonarSource/sonarqube-scan-action@v4
        env:
          SONAR_HOST_URL: http://sonar.institutoprominas.com.br
          SONAR_TOKEN: sqa_a8e606f6ea58bc9aafc226fad5dbba4b78ee1f05
        with:
          args: >
            -Dsonar.inclusions=${{ steps.changed-files.outputs.files }}

      - name: SonarQube Scan (full)
        if: steps.bypass-check.outputs.bypass == 'false' && steps.changed-files.outputs.has_changes == 'false'
        uses: SonarSource/sonarqube-scan-action@v4
        env:
          SONAR_HOST_URL: http://sonar.institutoprominas.com.br
          SONAR_TOKEN: sqa_a8e606f6ea58bc9aafc226fad5dbba4b78ee1f05

      - name: Quality Gate
        if: steps.bypass-check.outputs.bypass == 'false'
        uses: sonarsource/sonarqube-quality-gate-action@master
        id: sonar
        timeout-minutes: 5
        env:
          SONAR_HOST_URL: http://sonar.institutoprominas.com.br
          SONAR_TOKEN: sqa_a8e606f6ea58bc9aafc226fad5dbba4b78ee1f05

      - name: Bypass sonar success
        if: steps.bypass-check.outputs.bypass == 'true'
        run: |
          echo "Sonar verification bypassed successfully."

      - name: Trigger n8n webhook on sonar failure
        if: ${{ failure() && steps.sonar.outcome == 'failure' }}
        run: |
          commit_message=$(git log -1 --pretty=%B | tr -d '\n' | sed 's/"/\\"/g')
          auth_token=$(echo -n "github:github" | base64)
          repo_name=$(basename $GITHUB_REPOSITORY)
          logs_url="https://sonar.institutoprominas.com.br/dashboard?id=${repo_name}"

          curl -X POST https://n8n.institutoprominas.com.br/webhook/17433c29-9863-45ac-b648-8952575f0bd3 \
            -H 'Content-Type: application/json' \
            -H "Authorization: Basic ${auth_token}" \
            -d "{\"status\":\"failed\", \"message\":\"Sonar process failed!\", \"commit_message\":\"${commit_message}\", \"logs_url\":\"${logs_url}\"}"
