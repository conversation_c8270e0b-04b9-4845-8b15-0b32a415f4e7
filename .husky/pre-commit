# Garante que a branch atual está atualizada com a master

# Configura Node 20 usando nvm
echo "Verificando versão do Node..."
REQUIRED_NODE_VERSION="20"

# Verifica se o Node está instalado e qual versão
if command -v node >/dev/null 2>&1; then
  CURRENT_NODE_VERSION=$(node -v | sed 's/v//' | cut -d. -f1)
  echo "Versão atual do Node: v$(node -v | sed 's/v//')"
else
  CURRENT_NODE_VERSION=""
  echo "Node não encontrado"
fi

# Se não tiver Node 20, instala/usa via nvm
if [ "$CURRENT_NODE_VERSION" != "$REQUIRED_NODE_VERSION" ]; then
  echo "Node 20 não encontrado. Configurando via nvm..."

  # Carrega nvm se disponível
  if [ -s "$HOME/.nvm/nvm.sh" ]; then
    . "$HOME/.nvm/nvm.sh"
  elif [ -s "/usr/local/opt/nvm/nvm.sh" ]; then
    . "/usr/local/opt/nvm/nvm.sh"
  else
    echo "nvm não encontrado. Instalando nvm..."
    curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
    export NVM_DIR="$HOME/.nvm"
    [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
  fi

  # Instala Node 20 se não estiver disponível
  if ! nvm list | grep -q "v20"; then
    echo "Instalando Node 20..."
    nvm install 20
  fi

  # Usa Node 20
  echo "Usando Node 20..."
  nvm use 20
else
  echo "Node 20 já está em uso"
fi

git fetch origin master
CURRENT_BRANCH=$(git rev-parse --abbrev-ref HEAD)
if ! git log --oneline --graph master..$CURRENT_BRANCH > /dev/null; then
  echo "Sua branch está desatualizada em relação à master.  Há commits na master que não estão na sua branch."
  exit 1  # Falha o comando
fi

# Executa eslint nos arquivos alterados
CHANGED_JS_FILES_RAW=$(git diff --name-only master..$CURRENT_BRANCH | grep -E '\.(js|jsx|ts|tsx)$')
CHANGED_JS_FILES=""
for file in $CHANGED_JS_FILES_RAW; do
  if [ -f "$file" ]; then
    CHANGED_JS_FILES="$CHANGED_JS_FILES $file"
  fi
done
CHANGED_JS_FILES=$(echo "$CHANGED_JS_FILES" | xargs)
if [ -n "$CHANGED_JS_FILES" ]; then
  echo "Executando eslint nos arquivos alterados: $CHANGED_JS_FILES"
  npx eslint $CHANGED_JS_FILES --fix
else
  echo "Nenhum arquivo JavaScript alterado para análise do eslint."
fi

# Obtém o nome do repositório automaticamente
REPO_NAME=$(basename $(pwd))
echo "Nome do repositório: $REPO_NAME"

# Obtém a lista de arquivos alterados em relação à branch master
CHANGED_FILES_RAW=$(git diff --name-only master..$CURRENT_BRANCH)
CHANGED_FILES_FILTERED=""
for file in $CHANGED_FILES_RAW; do
  if [ -f "$file" ]; then
    if [ -z "$CHANGED_FILES_FILTERED" ]; then
      CHANGED_FILES_FILTERED="$file"
    else
      CHANGED_FILES_FILTERED="$CHANGED_FILES_FILTERED,$file"
    fi
  fi
done
CHANGED_FILES="$CHANGED_FILES_FILTERED"
echo "Arquivos alterados em relação à master: $CHANGED_FILES"

# Executa análise do SonarQube apenas nos arquivos alterados
echo "Executando análise do SonarQube apenas nos arquivos alterados..."
if [ -n "$CHANGED_FILES" ]; then
  npx sonarqube-scanner -Dsonar.inclusions="$CHANGED_FILES"
else
  echo "Nenhum arquivo alterado para análise."
  npx sonarqube-scanner
fi

# Configura variáveis de ambiente para o SonarQube
export SONAR_TOKEN="sqa_a8e606f6ea58bc9aafc226fad5dbba4b78ee1f05"
export SONAR_HOST_URL="https://sonar.institutoprominas.com.br"

# Verifica o status do Quality Gate usando o novo script
echo "Verificando o status do Quality Gate..."
METADATA_FILE=".scannerwork/report-task.txt"
POLLING_TIMEOUT=300  # 5 minutos de timeout

# Executa o script de verificação do Quality Gate
"$(dirname "$0")/../bash_scripts/sonar-quality-check.sh" "$METADATA_FILE" "$POLLING_TIMEOUT"
