module.exports = {
  async up(db, client) {
    await db.createCollection('teste');
    await db.createCollection('teste2');
    await db.collection('teste')
      .insertOne({des_1828: true});
    await db.collection('teste2')
      .insertOne({des_1828: true});

    // Exemplo com session, update em varias collections
    const session = client.startSession();

    try {
      await session.withTransaction(async () => {
        await db.collection('teste')
          .updateOne({des_1828: true}, {$set: {updatedFromSession: true}}, {session});
        await db.collection('teste2')
          .updateOne({des_1828: true}, {$set: {updatedFromSession: true}}, {session});
      });
    } finally {
      await session.endSession();
    }
  },

  async down(db, client) {
    await db.dropCollection('teste');
    await db.dropCollection('teste2');
  }
};
