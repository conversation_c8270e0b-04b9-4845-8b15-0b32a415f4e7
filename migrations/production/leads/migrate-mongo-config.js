// In this file you can configure migrate-mongo

const buildMongoUri = config => {
  const hosts = config.servers.map(server => server.host).join(',');
  const { user, pass, replicaSet, authSource, ssl } = config;

  return `mongodb://${user}:${pass}@${hosts}/?replicaSet=${replicaSet}&authSource=${authSource}&ssl=${ssl}`;
}

const getConnectionFromEnv = (cluster, db) => {
  const connectionKey = (`${cluster.replace(/-/gm, '_')}_${db}`).toUpperCase();
  const connectionJSON = process.env[connectionKey];

  if(!connectionJSON)
    throw new Error(`Conexão não encontrada (${connectionKey})`);

  const connectionObj = JSON.parse(connectionJSON);

  return buildMongoUri(connectionObj);
}

const config = {
  mongodb: {
    url: getConnectionFromEnv('leads', 'leads'),
    databaseName: "leads",

    options: {
      // useNewUrlParser: true, // removes a deprecation warning when connecting
      // useUnifiedTopology: true, // removes a deprecating warning when connecting
      //   connectTimeoutMS: 3600000, // increase connection timeout to 1 hour
      //   socketTimeoutMS: 3600000, // increase socket timeout to 1 hour
    }
  },

  // The migrations dir, can be an relative or absolute path. Only edit this when really necessary.
  migrationsDir: "scripts",

  // The mongodb collection where the applied changes are stored. Only edit this when really necessary.
  changelogCollectionName: "migrations_changelog",

  // The file extension to create migrations and search for in migration dir
  migrationFileExtension: ".js",

  // Enable the algorithm to create a checksum of the file contents and use that in the comparison to determine
  // if the file should be run.  Requires that scripts are coded to be run multiple times.
  useFileHash: false,

  // Don't change this, unless you know what you're doing
  moduleSystem: 'commonjs',
};

module.exports = config;
