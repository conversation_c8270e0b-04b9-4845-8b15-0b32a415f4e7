steps:
  # Criar pasta caso nao existir
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk:alpine'
    id: 'create-tmp-folder'
    entrypoint: 'gcloud'
    args: [
      'compute', 'ssh',
      'services@${_VM}',
      '--command', 'sudo rm -rf /tmp/${_FOLDER} && mkdir -p /tmp/${_FOLDER} && sudo chown -R services:services /tmp/${_FOLDER}',
      '--zone', '${_VM_REGION}', '--internal-ip'
    ]

  # Transferir arquivos diretamente para a VM usando gcloud compute scp
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk:alpine'
    id: 'copy-files'
    entrypoint: 'gcloud'
    args: [
      'compute', 'scp',
      '--recurse',
      '--compress',
      '--quiet',
      '/workspace',
      'services@${_VM}:/tmp/${_FOLDER}',
      '--zone', '${_VM_REGION}', '--internal-ip'
    ]

  # Instalar rsync e jq (se nao houver)
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk:alpine'
    id: 'install-rsync'
    entrypoint: 'gcloud'
    args: [
      'compute', 'ssh',
      'services@${_VM}',
      '--command', 'sudo apt-get install -y rsync jq',
      '--zone', '${_VM_REGION}', '--internal-ip'
    ]

  # Preparar package.json
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk:alpine'
    id: 'prepare-package-json'
    entrypoint: 'gcloud'
    args: [
      'compute', 'ssh',
      'services@${_VM}',
      '--command', 'cd /tmp/${_FOLDER}/workspace && export CMD="bash bash_scripts/prepare_package_json.sh ${_NODE_VERSION} ${_TEST_COMMAND}" && if [ "${_ROOT}" = "true" ]; then sudo $$CMD; else $$CMD; fi',
      '--zone', '${_VM_REGION}', '--internal-ip'
    ]

  # Instalar dependências
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk:alpine'
    id: 'install-dependencies'
    entrypoint: 'gcloud'
    args: [
      'compute', 'ssh',
      'services@${_VM}',
      '--command', 'cd /tmp/${_FOLDER}/workspace && bash bash_scripts/create_node_modules.sh ${_NODE_VERSION} ${_FOLDER}',
      '--zone', '${_VM_REGION}', '--internal-ip'
    ]

  # Build
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk:alpine'
    id: 'build'
    entrypoint: 'gcloud'
    args: [
      'compute', 'ssh',
      'services@${_VM}',
      '--command', 'cd /tmp/${_FOLDER}/workspace && bash bash_scripts/npm.sh ${_NODE_VERSION} ${_BUILD_COMMAND}',
      '--zone', '${_VM_REGION}', '--internal-ip'
    ]

  # Copiar models
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk:alpine'
    id: 'copy-models'
    entrypoint: 'gcloud'
    args: [
      'compute', 'ssh',
      'services@${_VM}',
      '--command', 'sudo cp -r ${_MODELS_LOCATION} /tmp/${_FOLDER} && sudo chown -R services:services /tmp/${_FOLDER}',
      '--zone', '${_VM_REGION}', '--internal-ip'
    ]

  # Verificar integridade
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk:alpine'
    id: 'verify-integrity'
    entrypoint: 'gcloud'
    args: [
      'compute', 'ssh',
      'services@${_VM}',
      '--command', 'cd /tmp/${_FOLDER}/workspace && export CMD="bash bash_scripts/npm.sh ${_NODE_VERSION} ${_TEST_COMMAND}" && if [ "${_ROOT}" = "true" ]; then sudo $$CMD; else $$CMD; fi',
      '--zone', '${_VM_REGION}', '--internal-ip'
    ]

  # Rodar migrations
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk:alpine'
    id: 'run-migrations'
    entrypoint: 'gcloud'
    args: [
      'compute', 'ssh',
      'services@${_VM}',
      '--command', 'cd /tmp/${_FOLDER}/workspace && export CMD="bash bash_scripts/migrations.sh ${_NODE_VERSION} ${_MIGRATIONS}" && if [ "${_ROOT}" = "true" ]; then sudo $$CMD; else $$CMD; fi',
      '--zone', '${_VM_REGION}', '--internal-ip'
    ]

  # Mover arquivos para o diretório final usando um script remoto
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk:alpine'
    id: 'move-files'
    entrypoint: 'gcloud'
    args: [
      'compute', 'ssh',
      'services@${_VM}',
      '--command', 'if [ "${_ROOT}" = "true" ]; then sudo rsync -av --remove-source-files /tmp/${_FOLDER}/workspace/* ${_LOCATION}/${_FOLDER}; else rsync -av --remove-source-files /tmp/${_FOLDER}/workspace/* ${_LOCATION}/${_FOLDER}; fi',
      '--zone', '${_VM_REGION}', '--internal-ip'
    ]

#  # Reiniciar o serviço PM2 na VM
#  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk:alpine'
#    id: 'restart-service'
#    entrypoint: 'gcloud'
#    args: [
#      'compute', 'ssh',
#      'services@${_VM}',
#      '--command', 'if [ "${_ROOT}" = "true" ]; then sudo bash ${_LOCATION}/${_FOLDER}/bash_scripts/pm2.sh "start ${_LOCATION}/${_FOLDER}/${_PM2_FILE}"; else sudo bash ${_LOCATION}/${_FOLDER}/bash_scripts/pm2.sh "start ${_LOCATION}/${_FOLDER}/${_PM2_FILE}"; fi',
#      '--zone', '${_VM_REGION}', '--internal-ip'
#    ]

  # Permissão para executar os scripts
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk:alpine'
    id: 'scripts-permissions'
    entrypoint: 'gcloud'
    args: [
      'compute', 'ssh',
      'services@${_VM}',
      '--command', 'sudo find ${_LOCATION}/${_FOLDER}/bash_scripts/ -type f -exec chmod +x {} \;',
      '--zone', '${_VM_REGION}', '--internal-ip'
    ]

    # Reiniciar o serviço PM2 na VM via YAML
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk:alpine'
    id: 'restart-service'
    entrypoint: 'gcloud'
    args:
      - 'compute'
      - 'ssh'
      - 'services@${_VM}'
      - '--command'
      - |
        if [ "${_ROOT}" = "true" ]; then
          sudo bash -c 'cd ${_LOCATION}/${_FOLDER} && ${_LOCATION}/${_FOLDER}/bash_scripts/pm2.sh "start ${_LOCATION}/${_FOLDER}/${_PM2_FILE}"';
        else
          bash -c 'cd ${_LOCATION}/${_FOLDER} && ${_LOCATION}/${_FOLDER}/bash_scripts/pm2.sh "start ${_LOCATION}/${_FOLDER}/${_PM2_FILE}"';
        fi
      - '--zone'
      - '${_VM_REGION}'
      - '--internal-ip'

  # Notificar sucesso no Discord
  - name: 'curlimages/curl'
    id: 'notify-success'
    entrypoint: 'curl'
    args: [
      '-X', 'POST', '$_DISCORD_WEBHOOK_URL',
      '-H', 'Content-Type: application/json',
      '--data', '{"content": ":white_check_mark: Build concluído com sucesso! Build: $TRIGGER_NAME ($REF_NAME). Veja os logs: https://console.cloud.google.com/cloud-build/builds;region=$LOCATION/$BUILD_ID?project=$PROJECT_ID"}'
    ]
    waitFor: [ 'move-files', 'restart-service' ]

timeout: '1200s'

options:
  logging: 'CLOUD_LOGGING_ONLY'
  pool:
    name: projects/erp-prominas/locations/us-west1/workerPools/builds

# Variaveis de substituição
substitutions:
  _VM: 'homologacao'
  _VM_REGION: 'us-west1-a'
  _ROOT: 'true'
  _NODE_VERSION: '20.16.0'
  _MIGRATIONS: 'homologation'
  _LOCATION: '/opt/piaget/backend'
  _FOLDER: 'microservices_vestibular'
  _PM2_NAME: 'api_vestibular_homo'
  _PM2_FILE: 'pm2-deploy-prominas-homo.yml'
  _BUILD_COMMAND: 'build-homo:prominas'
  _TEST_COMMAND: 'test-build-homo:prominas'
  _MODELS_LOCATION: '/opt/piaget/backend/piaget_models_20'
  _DISCORD_WEBHOOK_URL: 'https://discord.com/api/webhooks/1279076153616896080/nkdczu_0_sNdwRnxGyFLMrXawXOWysbWazcH7Kdf01YgxkwgK-wt2yoCM7pr3k5tL60V'
