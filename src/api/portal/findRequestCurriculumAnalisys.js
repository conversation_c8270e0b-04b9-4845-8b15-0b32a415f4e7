import { Types } from "mongoose";

export default async (req, res) => {
  const {DispensationSolicitations} = req.dbs.database_piaget.models;

  try {
    const data = await DispensationSolicitations.find({
      _enrolmentId: {$in: req.query.enrolmentIds.split(',').map(id => new Types.ObjectId(id))}
    });

    return res.api.send(data, res.api.codes.OK);
  } catch (err) {
    return res.api.send(`${err}`, res.api.codes.INTERNAL_SERVER_ERROR);
  }
}
