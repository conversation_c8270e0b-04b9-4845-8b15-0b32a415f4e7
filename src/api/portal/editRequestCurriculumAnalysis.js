import {Types} from "mongoose";

const editRequestCurriculumAnalisys = async(req, res) => {
  const { DispensationSolicitations } = req.dbs.database_piaget.models;

  const cannotEdit = await DispensationSolicitations.findOne({
    _id: new Types.ObjectId(req.params._id),
    status: {$ne: 'waiting'}
  }, {_id: 1});

  if (cannotEdit)
    return res.api.send('A solicitação se encontra em análise ou finalizada', res.api.codes.BAD_REQUEST);

  return DispensationSolicitations.findOneAndUpdate({_id: new Types.ObjectId(req.params._id)}, req.body)
      .then(dispensationSolicitation => res.api.send(dispensationSolicitation, res.api.codes.OK))
      .catch(err => res.api.send(err.stack, res.api.codes.INTERNAL_SERVER_ERROR));
};

export default editRequestCurriculumAnalisys;
