import {Types} from "mongoose";
import moment from "moment";

const getRandomItems = (array, itemsNumber) => {
  // Cria um novo array vazio para armazenar os itens aleatórios
  const itensAleatorios = [];

  // Verifica se o número de itens solicitados é maior que o tamanho do array, se for retorna o proprio array completo
  if (itemsNumber >= array.length)
    return array;

  // Loop para obter o número solicitado de itens aleatórios
  for (let i = 0; i < itemsNumber; i++) {
    // Gera um índice aleatório
    const indiceAleatorio = Math.floor(Math.random() * array.length);

    // Adiciona o item aleatório ao novo array
    itensAleatorios.push(array[indiceAleatorio]);

    // Remove o item do array original para evitar duplicatas
    array.splice(indiceAleatorio, 1);
  }

  // Retorna o array com os itens aleatórios
  return itensAleatorios;
};

// const getFinishedEvaluation = async (models, studentId, processId, courseId) => {
//   const {SelectiveProcessStudentsEvaluations} = models;

//   const haveEvaluation = await SelectiveProcessStudentsEvaluations.findOne({
//     _studentId: new Types.ObjectId(studentId),
//     _processSelectiveId: new Types.ObjectId(processId),
//     _courseId: new Types.ObjectId(courseId),
//     finishedAt: {$exists: true},
//   });

//   return haveEvaluation ? haveEvaluation.toObject() : undefined;
// };

const getCurrentEvaluation = async (models, studentId, processId, courseId) => {
  const {SelectiveProcessStudentsEvaluations} = models;

  // Verifica se o processo seletivo do estudante está em andamento
  const haveEvaluation = await SelectiveProcessStudentsEvaluations.findOne({
    _studentId: new Types.ObjectId(studentId),
    _processSelectiveId: new Types.ObjectId(processId),
    _courseId: new Types.ObjectId(courseId),
    startedAt: {$exists: true},
    finishedAt: {$exists: false},
  }).lean();

  return haveEvaluation;
};

const formatEvaluationData = (evaluationData) => {
  return Object.assign(evaluationData, {
    closedQuestions: evaluationData.closedQuestions
      .map(question => Object.assign(question, {
        alternatives: question.alternatives.map(alternative => Object.assign(alternative, {
          correctAlternative: undefined
        }))
      }))
  });
};

export default async (req, res) => {
  const {studentId} = req.tokenPayload;
  const {processId, courseId} = req.params;

  try {
    const {
      SelectiveProcessStudents,
      SelectiveProcess,
      SelectiveProcessStudentsEvaluations
    } = req.dbs.database_piaget.models;

    const canDoEvaluation = !!(await SelectiveProcessStudents.findOne({
      'student._id': new Types.ObjectId(studentId),
      'selectiveProcess._id': new Types.ObjectId(processId),
      courses: {$elemMatch: {courseId: new Types.ObjectId(courseId), status: 'waiting'}},
    }));

    if (!canDoEvaluation)
      return res.api.send('Voce não pode realizar avaliação', res.api.codes.BAD_REQUEST);

    // Comentei essa parte para permitir refazer caso seja finalizado mas o funcionario marcar no gestor que pode refazer
    /*const finishedEvaluation = await getFinishedEvaluation(req.dbs.database_piaget.models, studentId, processId, courseId);

    if(finishedEvaluation)
      return res.api.send('Voce já realizou a avaliação', res.api.codes.BAD_REQUEST);*/

    const alreadyStartedEvaluation = await getCurrentEvaluation(req.dbs.database_piaget.models, studentId, processId, courseId);
    if (alreadyStartedEvaluation) {
      const passedMinutes = moment().diff(moment(alreadyStartedEvaluation.startedAt), 'minutes');
      if (passedMinutes >= alreadyStartedEvaluation.maximumDuration && alreadyStartedEvaluation.maximumDuration !== -1)
        return res.api.send('Você não pode retomar a avaliação, tempo limite atingido', res.api.codes.BAD_REQUEST);
      else {
        alreadyStartedEvaluation.maximumDuration = alreadyStartedEvaluation.maximumDuration - passedMinutes;
        alreadyStartedEvaluation.returnedEvaluation = true;

        return res.api.send(formatEvaluationData(alreadyStartedEvaluation), res.api.codes.OK);
      }
    }

    const [processSelective] = await SelectiveProcess.aggregate([
      {
        $match: {
          _id: new Types.ObjectId(processId)
        },
      },
      {
        $addFields: {
          course: {
            $first: {
              $filter: {
                input: "$courses",
                as: "course",
                cond: {
                  $eq: [
                    "$$course.courseId",
                    new Types.ObjectId(courseId)
                  ]
                }
              }
            }
          }
        }
      },
      {
        $addFields:
          {
            'course.groupQuestion._id': {"$toObjectId": "$course.groupQuestion._id"}
          }
      },
      {
        $lookup: {
          from: 'GroupQuestions',
          localField: 'course.groupQuestion._id',
          foreignField: '_id',
          as: 'groupQuestion'
        },
      },
      {$unwind: '$groupQuestion'}
    ]);

    const numberOfCloseQuestions = processSelective.course.groupQuestion.closeQuestion;
    const numberOfOpenQuestions = processSelective.course.groupQuestion.openQuestion;
    const numberOfUploads = processSelective.course.groupQuestion.archiveQuestion;

    const closedQuestions = getRandomItems(
      processSelective.groupQuestion.questions
        .filter(question => question.isActive && question.typeOfAnswer === 'select'),
      numberOfCloseQuestions
    );

    const openQuestions = getRandomItems(
      processSelective.groupQuestion.questions
        .filter(question => question.isActive && question.typeOfAnswer === 'text'),
      numberOfOpenQuestions
    );

    const archiveQuestions = getRandomItems(
      processSelective.groupQuestion.questions
        .filter(question => question.isActive && question.typeOfAnswer === 'file'),
      numberOfUploads
    );

    const toCreateEvaluationData = {
      _processSelectiveId: processId,
      _studentId: studentId,
      _courseId: courseId,
      maximumDuration: processSelective.course.maximumDuration,
      descriptionForProof: processSelective.course.descriptionForProof,
      closedQuestions: closedQuestions
        .map(question => ({
          _id: question._id,
          utterance: question.utterance,
          alternatives: question.alternatives
            .map(alternative => ({
              _id: alternative._id,
              utterance: alternative.utteranceAlternative,
              correctAlternative: alternative.correctAlternative
            }))
        })),
      openQuestions,
      archiveQuestions,
    };

    const evaluationData = await SelectiveProcessStudentsEvaluations.create(toCreateEvaluationData);

    evaluationData.returnedEvaluation = false;

    return res.api.send(formatEvaluationData(evaluationData), res.api.codes.OK);
  } catch (err) {
    console.log(err);
    return res.api.send(`${err}`, res.api.codes.INTERNAL_SERVER_ERROR);
  }
}
