import {Types} from "mongoose";

export default async (req, res) => {
  const {studentId} = req.tokenPayload;
  const {evaluationId} = req.params;

  try {
    const {
      SelectiveProcessStudentsEvaluations
    } = req.dbs.database_piaget.models;

    const selectiveProcessStudents = await SelectiveProcessStudentsEvaluations.updateOne({
      _id: new Types.ObjectId(evaluationId),
      _studentId: new Types.ObjectId(studentId),
      startedAt: {$exists: false}
    }, {
      $set: {
        startedAt: new Date()
      }
    });

    return res.api.send(!!selectiveProcessStudents && !!selectiveProcessStudents.startedAt, res.api.codes.OK);
  } catch (err) {
    console.log(err);
    return res.api.send(`${err}`, res.api.codes.INTERNAL_SERVER_ERROR);
  }
}
