/* eslint-disable array-element-newline */
import <PERSON><PERSON> from 'joi';

export default (req, res, next) => {
  return Joi.object({
    finishedByStudent: Joi.boolean(),
    closedQuestions: Joi.array()
      .items(Joi.object({
        _id: Joi.string().required(),
        answer: Joi.string()
          .allow('')
          .required()
      }))
      .required(),
    openQuestions: Joi.array()
      .items(Joi.object({
        _id: Joi.string().required(),
        answer: Joi.string()
          .allow('')
          .required()
      }))
      .required(),
    archiveQuestions: Joi.array()
      .items(Joi.object({
        _id: Joi.string().required(),
        answer: Joi.string()
          .allow('')
          .required()
      }))
      .required()
  })
    .validate(req.body, (err) => {
      if (err)
        return res.api.send(
          err.details,
          res.api.codes.UNPROCESSABLE_ENTITY
        );

      return next();
    });
};
