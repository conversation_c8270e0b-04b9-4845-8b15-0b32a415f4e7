import Joi from 'joi';

export default (req, res, next) => {
  return Joi.object({
    _id: Joi.string().optional(),
    _userId: Joi.string().required(),
    _userType: Joi.string().valid("student", "partner", "teacher", "employer", "computer").required(),
    _userName: Joi.string().required(),
    _enrolmentId: Joi.string().required(),
    observation: Joi.string().optional().allow(""),
    dispensationTypes: Joi.string(),
    origin: Joi.string().required(),
    archives: Joi.array().items(({
      name: Joi.string().valid("academic_history", "syllabus").required(),
      url: Joi.string().required(),
      upload: Joi.date().optional(),
      metadata: Joi.object({
        filename: Joi.string()
      }).optional()
    })).min(1).required()
  })
  .validate(req.body, (err) => {
    if (err)
      return res.api.send(
        err.details,
        res.api.codes.UNPROCESSABLE_ENTITY
      );

    return next();
  });
}
