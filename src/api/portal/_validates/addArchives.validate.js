/* eslint-disable array-element-newline */
import <PERSON><PERSON> from 'joi';

export default (req, res, next) => {
  return Joi.alternatives().try(Joi.object({
    archives: Joi.array().items(Joi.string())
      .min(1)
      .required()
  }), Joi.object({
    enrolmentId: Joi.string().required()
  }))
    .validate(req.body, (err) => {
      if (err)
        return res.api.send(
          err.details,
          res.api.codes.UNPROCESSABLE_ENTITY
        );

      return next();
    });
};
