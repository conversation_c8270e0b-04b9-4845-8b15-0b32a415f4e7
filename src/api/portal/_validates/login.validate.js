/* eslint-disable array-element-newline */
import <PERSON><PERSON> from 'joi';

export default (req, res, next) => {
  return Joi.alternatives().try(Joi.object({
    cpf: Joi.string().required(),
    birthdate: Joi.date().required(),
    selectiveProcess: Joi.string()
  }), Joi.object({
    enrolmentId: Joi.string().required()
  }))
.validate(req.body, (err) => {
    if (err)
      return res.api.send(
        err.details,
        res.api.codes.UNPROCESSABLE_ENTITY
      );

    return next();
  });
};
