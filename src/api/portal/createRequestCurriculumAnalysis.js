import {Types} from "mongoose";

const createRequestCurriculumAnalisys = async (req, res) => {
  const { DispensationSolicitations, Enrolments } = req.dbs.database_piaget.models;

  if (req.body._id) {
    const enrolmentWithClass = await Enrolments.findOne({
      _id: new Types.ObjectId(req.body._enrolmentId),
      'registryCourse.course.class._id': {$exists: true}
    }, {_id: 1});

    if (enrolmentWithClass)
      return res.api.send('O aluno já foi enturmado, reanalise não permitida.');

    const dispensationSolicitation = await DispensationSolicitations.findOne({_id: new Types.ObjectId(req.body._id)});

    if (dispensationSolicitation.reanalysis?.length >= 3)
      return res.api.send('Limite de reanálise atingido', res.api.codes.BAD_REQUEST);

    const before = {
      createdAt: dispensationSolicitation.createdAt,
      origin: dispensationSolicitation.origin,
      archives: dispensationSolicitation.archives,
      reason: dispensationSolicitation.reason,
      dispensationTypes: dispensationSolicitation.dispensationTypes,
      observation: dispensationSolicitation.observation,
      _userId: dispensationSolicitation._userId,
      _userType: dispensationSolicitation._userType,
      _userName: dispensationSolicitation._userName,
    };

    req.body.createdAt = new Date();

    return DispensationSolicitations.updateOne({_id: new Types.ObjectId(req.body._id)}, {
      $set: {
        ...req.body,
        status: 'waiting'
      },
      $push: {
        reanalysis: {
          before,
          after: {...before, ...req.body}
        }
      }
    }).then(dispensationSolicitation => res.api.send(dispensationSolicitation, res.api.codes.OK))
        .catch(err => res.api.send(err.stack, res.api.codes.INTERNAL_SERVER_ERROR));
  }

  const alreadyHaveSolicitation = await DispensationSolicitations.findOne({
    _enrolmentId: new Types.ObjectId(req.body._enrolmentId),
  }, {_id: 1});

  if (alreadyHaveSolicitation)
    return res.api.send('Você já tem uma solicitação para essa inscrição, caso precise, você pode solicitar uma reavaliação', res.api.codes.BAD_REQUEST);

  return DispensationSolicitations.create(req.body)
    .then((result) => {
      return res.api.send(result, res.api.codes.CREATED);
    })
    .catch((err) => {
      return res.api.send(err.stack, res.api.codes.INTERNAL_SERVER_ERROR);
    });
};

export default createRequestCurriculumAnalisys;
