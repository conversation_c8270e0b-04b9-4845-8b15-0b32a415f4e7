import {Types} from "mongoose";
import moment from "moment";

export default async (req, res) => {
  const {SelectiveProcessStudents, Charges} = req.dbs.database_piaget.models;

  const {studentId} = req.tokenPayload;

  try {
    const selectiveProcesses = await SelectiveProcessStudents.aggregate([
      {
        $match: {
          'student._id': new Types.ObjectId(studentId),
        }
      },
      {
        $lookup: {
          from: 'SelectiveProcess',
          localField: 'selectiveProcess._id',
          foreignField: '_id',
          as: 'selectiveProcess'
        }
      },
      {$unwind: '$selectiveProcess'},
      {
        $match: {
          'selectiveProcess.dateStart': {$lte: moment().startOf('day').toDate()},
          'selectiveProcess.dateEnd': {$gte: moment().subtract(30, 'days').endOf('day').toDate()},
        }
      },
      {$sort: {createdAt: -1}}
    ]);

    const enrolmentIds = [];

    for (const selectiveProcess of selectiveProcesses) {
      for (const course of selectiveProcess.courses) {
        if (course.enrolmentId) enrolmentIds.push(new Types.ObjectId(course.enrolmentId));

        if (course.charge && course.charge._id) {
          const charge = await Charges.findById(course.charge._id);
          
          if (charge.status === 'paid' || charge.status === 'free') course.charge.isPayment = true;
        }
      }
    }

    if (enrolmentIds.length > 0) {
      const notes = await req.dbs.database_piaget.models.EnrolmentNotes.find({_enrolmentId: {$in: enrolmentIds}}).lean();

      selectiveProcesses.map(item => {
        item.courses.map(course => {
          course.notes = [];

          notes.map(note => {
            if (note._enrolmentId.toString() === course.enrolmentId.toString()) course.notes.push(note);
          })
        })
      });

    }

    return res.api.send(selectiveProcesses, res.api.codes.OK);
  } catch (err) {
    console.log(err);
    return res.api.send(`${err}`, res.api.codes.INTERNAL_SERVER_ERROR);
  }
}
