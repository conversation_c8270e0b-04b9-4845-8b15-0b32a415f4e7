import {Types} from "mongoose";

export default async (req, res) => {
  const {SelectiveProcessStudents} = req.dbs.database_piaget.models;

  const {studentId} = req.tokenPayload;
  const {selectiveProcessStudentId, courseId} = req.params;

  try {
    const selectiveProcessStudent = await SelectiveProcessStudents.findOne({
      _id: new Types.ObjectId(selectiveProcessStudentId),
      'student._id': new Types.ObjectId(studentId),
      courses: {
        $elemMatch: {
          _id: new Types.ObjectId(courseId),
          status: 'waiting'
        }
      }
    });

    if (!selectiveProcessStudent) return res.api.send('Processo seletivo não encontrado ou já finalizado', res.api.codes.BAD_REQUEST);

    return SelectiveProcessStudents.findOneAndUpdate({
      _id: new Types.ObjectId(selectiveProcessStudentId),
      'student._id': new Types.ObjectId(studentId),
      courses: {
        $elemMatch: {
          _id: new Types.ObjectId(courseId),
          status: 'waiting'
        }
      }
    }, {
      $set: {
        'courses.$.documentsSent.files': selectiveProcessStudent.courses.find(c => c._id.toString() === courseId).documentsSent.files.concat(req.body.archives.map(archive => ({
          url: archive,
          type: 'Outros',
          name: 'Arquivo enviado pelo portal do candidato',
        })))
      }
    }, {new: true})
      .then(result => res.api.send(result, res.api.codes.OK))
      .catch(err => res.api.send(err, res.api.codes.INTERNAL_SERVER_ERROR));
  } catch (err) {
    console.log(err);
    return res.api.send(`${err}`, res.api.codes.INTERNAL_SERVER_ERROR);
  }
}
