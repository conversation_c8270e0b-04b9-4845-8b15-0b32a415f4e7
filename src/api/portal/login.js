import moment from "moment";
import JwtService from "../../services/Jwt.service";
import {Types} from "mongoose";

export default async (req, res) => {
  const {SelectiveProcessStudents, Students, Enrolments} = req.dbs.database_piaget.models;

  try {
    if (req.body.enrolmentId) {
      const enrolment = await Enrolments.aggregate([
        {$match: {_id: new Types.ObjectId(req.body.enrolmentId)}},
        {
          $lookup: {
            from: 'SelectiveProcessStudents',
            localField: 'cpf',
            foreignField: 'student.cpf',
            as: 'selectiveProcessStudents'
          }
        },
        {$unwind: '$selectiveProcessStudents'},
        {$match: req.body.selectiveProcess ? {'selectiveProcessStudents.selectiveProcess._id' : req.body.selectiveProcess} : {}}
      ]);

      if (!enrolment) return res.api.send('Login inválido', res.api.codes.BAD_REQUEST);

      req.body.cpf = enrolment[0].cpf;
      req.ignoreBirthdate = true;
    }

    const match = {
      'student.cpf': req.body.cpf
    };

    if (!req.ignoreBirthdate)
      match['student.birthdate'] = {
        $gte: moment(req.body.birthdate).startOf('day'),
        $lte: moment(req.body.birthdate).endOf('day'),
      }

    const canLogin = !!(await SelectiveProcessStudents.findOne(match));

    if (!canLogin) return res.api.send('Login inválido', res.api.codes.BAD_REQUEST);

    const student = await Students.findOne({cpf: req.body.cpf});

    const token = new JwtService().createBearerToken({
      studentId: student._id,
      selectiveProcessId: req.body.selectiveProcess
    }, '1d');

    return res.api.send({token}, res.api.codes.OK);
  } catch (err) {
    console.log(err);
    return res.api.send(`${err}`, res.api.codes.INTERNAL_SERVER_ERROR);
  }
}
