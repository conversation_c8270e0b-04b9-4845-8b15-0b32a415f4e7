import loggedPortal from '../../guards/loggedPortal';
import checkReCaptcha from '../../middlewares/checkReCaptcha';
import addArchivesValidate from './_validates/addArchives.validate';
import finishEvaluationValidate from './_validates/finishEvaluation.validate';
import LoginValidate from './_validates/login.validate';
import requestCurriculumAnalysisValidate from './_validates/requestCurriculumAnalysis.validate';
import addArchives from './addArchives';
import createRequestCurriculumAnalisys from './createRequestCurriculumAnalysis';
import readEvaluationData from './evaluationData';
import findRequestCurriculumAnalisys from './findRequestCurriculumAnalisys';
import finishEvaluation from './finishEvaluation';
import login from './login';
import readMyData from './myData';
import readMyExams from './myExams';
import startEvaluation from './startEvaluation';
import editRequestCurriculumAnalysis from "./editRequestCurriculumAnalysis";
import editCurriculumAnalysisValidate from "./_validates/editCurriculumAnalysis.validate";

export default (route) => {
  const resource = '/portal';

  // Rota para realizar login
  route.post(`${resource}/login`, [
    checkReCaptcha('vestibular', 0.7),
    LoginValidate,
    login
  ]);

  // Rota para realizar login com enrolmentId
  route.post(`${resource}/login-with-enrolment`, [
    LoginValidate,
    login
  ]);

  // Rota para ler dados de um aluno
  route.get(`${resource}/data`, [
    loggedPortal,
    readMyData
  ]);

  // Rota para ler processos seletivos de um aluno
  route.get(`${resource}/exams`, [
    loggedPortal,
    readMyExams
  ]);

  // Rota para ler dados da avaliacao
  route.get(`${resource}/evaluation-data/:processId/:courseId`, [
    loggedPortal,
    readEvaluationData
  ]);

  // Rota para iniciar avaliacao
  route.get(`${resource}/start-evaluation/:evaluationId`, [
    loggedPortal,
    startEvaluation
  ]);

  // Rota para finalizar avaliacao
  route.post(`${resource}/finish-evaluation/:evaluationId`, [
    loggedPortal,
    finishEvaluationValidate,
    finishEvaluation
  ]);

  // Rota para adicionar arquivo ao processo de avaliacao
  route.post(`${resource}/add-archive/:selectiveProcessStudentId/:courseId`, [
    loggedPortal,
    addArchivesValidate,
    addArchives
  ]);

  // Rota para cadastrar solicitação de análise curricular
  route.post(`${resource}/request-curriculum-analysis`, [
    loggedPortal,
    requestCurriculumAnalysisValidate,
    createRequestCurriculumAnalisys
  ]);

  // Rota para editar solicitação de análise curricular
  route.put(`${resource}/request-curriculum-analysis/:_id`, [
    loggedPortal,
    editCurriculumAnalysisValidate,
    editRequestCurriculumAnalysis
  ]);

  // Rota para buscar as solicitações de análises curriculares
  route.get(`${resource}/request-curriculum-analysis`, [
    loggedPortal,
    findRequestCurriculumAnalisys
  ]);
}
