import {Types} from 'mongoose';
import moment from 'moment';
import ApproveService from '../../services/Aprove.service';
import UserValidator from '../../services/UserValidator.service';
import ApiRequestService from '../../services/ApiRequest.service';

export default async (req, res) => {
  const {studentId} = req.tokenPayload;
  const {evaluationId} = req.params;

  try {
    const {
      SelectiveProcessStudentsEvaluations,
      SelectiveProcess,
      SelectiveProcessStudents,
      EnrolmentClassEntry,
      Classes,
      Enrolments,
      DispensationSolicitations,
      Grouping,
      EnrolmentNotes,
    } = req.dbs.database_piaget.models;

    let selectiveProcessStudentsEvaluation = await SelectiveProcessStudentsEvaluations.findOne({
      _id: new Types.ObjectId(evaluationId),
      _studentId: new Types.ObjectId(studentId),
      finishedAt: {$exists: false},
    });

    if (!selectiveProcessStudentsEvaluation)
      return res.api.send('Avaliação não encontrada ou já finalizada', res.api.codes.BAD_REQUEST);

    const allQuestions = selectiveProcessStudentsEvaluation.closedQuestions.concat(selectiveProcessStudentsEvaluation.openQuestions).concat(selectiveProcessStudentsEvaluation.archiveQuestions);
    const allUserQuestions = req.body.closedQuestions.concat(req.body.openQuestions).concat(req.body.archiveQuestions);

    const validAnswers = allQuestions.length === allUserQuestions.length && !!allQuestions.find(question => !!allUserQuestions.find(q => q._id.toString() === question._id.toString()));

    if (!validAnswers)
      return res.api.send('Respostas inválidas', res.api.codes.BAD_REQUEST);

    selectiveProcessStudentsEvaluation = await SelectiveProcessStudentsEvaluations.findOneAndUpdate({
      _id: new Types.ObjectId(evaluationId),
    }, {
      $set: {
        answers: req.body,
        finishedAt: new Date(),
      },
    });

    const selectiveProcess = await SelectiveProcess.findById(selectiveProcessStudentsEvaluation._processSelectiveId);
    const selectiveProcessStudent = await SelectiveProcessStudents.findOne({
      'student._id': selectiveProcessStudentsEvaluation._studentId,
      'selectiveProcess._id': selectiveProcessStudentsEvaluation._processSelectiveId,
    });
    const selectiveProcessStudentCourse = selectiveProcessStudent.courses.find(course => course.courseId.toString() === selectiveProcessStudentsEvaluation._courseId.toString());

    const updateSelectiveProcessStudentCourse = {
      quiz: {
        dateStart: selectiveProcessStudentsEvaluation.startedAt,
        questions: selectiveProcessStudentsEvaluation.closedQuestions.map(closedQuestion => {
          return {
            typeOfAnswer: 'select',
            questionId: closedQuestion._id,
            name: 'Questao de multipla escolha',
            utterance: closedQuestion.utterance,
            responseOption: allUserQuestions.find(question => question._id.toString() === closedQuestion._id.toString()).answer,
            alternatives: closedQuestion.alternatives.map(alternative => ({
              alternativeId: alternative._id,
              utteranceAlternative: alternative.utterance,
              correctAlternative: alternative.correctAlternative,
            })),
          };
        }).concat(selectiveProcessStudentsEvaluation.openQuestions.map(closedQuestion => {
          return {
            typeOfAnswer: 'text',
            questionId: closedQuestion._id,
            name: 'Questao aberta',
            utterance: closedQuestion.utterance,
            responseText: allUserQuestions.find(question => question._id.toString() === closedQuestion._id.toString()).answer,
          };
        })),
      },
      documentsSent: {
        files: selectiveProcessStudentsEvaluation.archiveQuestions.map(question => {
          const archiveQuestion = allUserQuestions.find(_question => _question._id.toString() === question._id.toString());

          return {
            _id: archiveQuestion._id,
            url: archiveQuestion.answer,
            name: question.utterance,
          };
        }),
      },
    };

    updateSelectiveProcessStudentCourse.quiz.type = 'Prova Online';
    updateSelectiveProcessStudentCourse.quiz.dateEnd = new Date();
    updateSelectiveProcessStudentCourse.quiz.wasFixedAutomatically = true;
    updateSelectiveProcessStudentCourse.quiz.finishedByStudent = 'finishedByStudent' in req.body ? req.body.finishedByStudent : true;

    let classe = undefined;

    // Fazer correcao automatica
    if (selectiveProcess.autoCorrection) {
      // Corrige questoes fechadas (multipla escolha)
      req.body.closedQuestions.forEach(question => {
        const correctAnswer = allQuestions.find(closedQuestion => closedQuestion._id.toString() === question._id.toString()).alternatives.find(alternative => alternative.correctAlternative);

        updateSelectiveProcessStudentCourse.quiz.questions.find(q => q.questionId.toString() === question._id.toString()).correct = question.answer === correctAnswer._id.toString();
      });

      // Avaliacao pode ser corrigida automatico totalmente
      if (req.body.closedQuestions.length === allQuestions.length) {
        //if (req.body.closedQuestions.length > 0 && req.body.openQuestions.length === 0 && req.body.archiveQuestions.length === 0) {
        const maxHits = req.body.closedQuestions.length;
        const hits = updateSelectiveProcessStudentCourse.quiz.questions.filter(q => !!q.correct).length;

        const grade = parseFloat((hits * 100 / maxHits).toFixed(2));
        const percentageApproval = parseFloat(selectiveProcess.percentageApproval);

        updateSelectiveProcessStudentCourse.quiz.hitPercentage = grade;
        updateSelectiveProcessStudentCourse.status = grade > percentageApproval ? !updateSelectiveProcessStudentCourse.charge || updateSelectiveProcessStudentCourse.charge.isPayment ? 'approved' : 'waiting' : 'disapproved';
        updateSelectiveProcessStudentCourse['inputMethod.approved'] = grade > percentageApproval;

        const selectiveProcessCourse = selectiveProcess.courses.find(course => course.courseId.toString() === selectiveProcessStudentsEvaluation._courseId.toString());

        // selectiveProcessCourse.classes = [{classId:new Types.ObjectId("68307a204be4fc075afc85ea")}]// fixme remover

        if (
          updateSelectiveProcessStudentCourse.status === 'approved' &&
          selectiveProcessCourse.classes &&
          Array.isArray(selectiveProcessCourse.classes) &&
          selectiveProcessCourse.classes.length > 0
        ) {
          classe = await Classes.findOne({
            _id: {$in: selectiveProcessCourse.classes.map(classe => classe.classId)},
            dateStartEnrolment: {$lte: moment().startOf('day').toDate()},
            dateEndEnrolment: {$gte: moment().startOf('day').toDate()},
            vacancies: {$gt: 0},
          });

          if (classe) {
            const disciplines = [];
            const optionalDisciplines = [];
            const disciplinesWIthDispensation = [];
            const dispensationSolicitation = await DispensationSolicitations.find(
              {
                _enrolmentId: new Types.ObjectId(selectiveProcessStudentCourse.enrolmentId),
                status: 'approved',
              },
            ).lean();

            (dispensationSolicitation || []).map(solicitation => {
              (solicitation.disciplines || []).map(discipline => {
                disciplinesWIthDispensation.push(Object.assign(discipline, {dispensationTypes: solicitation.dispensationTypes}));
              });
            });
            const referenceMatrixIds = dispensationSolicitation.map(solicitation => ((solicitation || {}).referenceMatrix || {})._id).filter(item => !!item);

            if (disciplinesWIthDispensation.length && referenceMatrixIds.length) {
              const equivalents = await req.models.EquivalentsDisciplines.find({
                'curriculumMatrix._id': referenceMatrixIds.map(item => new Types.ObjectId(item)),
                _name: {$in: disciplinesWIthDispensation.map(item => item.name || item._name)},
              }).lean();
              const copyDispensationSolicitation = JSON.parse(JSON.stringify(disciplinesWIthDispensation));
              equivalents.map(item => {
                const equivalentDispensation = copyDispensationSolicitation.find(equivalent => (equivalent._name || equivalent.name) === (item._name || item.name));

                if (equivalentDispensation) disciplinesWIthDispensation.push(Object.assign(equivalentDispensation, {
                  name: item.equivalents._name || item.equivalents.name,
                  discId: item.equivalents.discId,
                }));
              });
            }

            for (let i = 0; i < (classe.disciplines || []).length; i++) {
              const disc = classe.disciplines[i];
              let enrolmentDiscipline = disc;
              let groupDiscipline = false;
              let grouping = {};

              if (disc.grouping) {
                grouping = await Grouping.findById(disc.grouping._id || disc.grouping.groupId);
                if (grouping) {
                  groupDiscipline = true;
                  if (grouping.teachingPlanByDiscipline === false) {
                    enrolmentDiscipline = grouping;
                  }
                }
              }

              const objDiscipline = {
                isOnRecuperation: disc.isOnRecuperation || false,
                grade: null,
                status: 'in_progress',
                gradeType: disc.gradeType || 'standard',
                gradeHistory: [],
                _name: enrolmentDiscipline.name,
                period: disc.module,
                discId: disc.discId,
                type: disc.type || 'required',
                description: groupDiscipline ? enrolmentDiscipline.description : enrolmentDiscipline.name,
                sagahDiscipline: enrolmentDiscipline.sagahDiscipline,
                workload: disc.workload,
                forums: enrolmentDiscipline.forums.map(v => ({
                  forumId: v.forumId,
                  title: v.title,
                  type: v.type,
                  grade: null,
                })),
                directedStudies: (enrolmentDiscipline.directedStudies || []).map(v => ({
                  directedStudyId: v.directedStudyId,
                  title: v.title,
                  type: v.type,
                  grade: null,
                })),
                disciplineClass: {
                  _id: disc._id,
                  code: disc.code,
                  name: disc.name,
                },
                activities: enrolmentDiscipline.activities.map(v => {
                  const data = {
                    type: v.type,
                    modality: v.modality ? v.modality : 'online',
                    maxDuration: v.maxDuration,
                    model: v.model,
                    isFinalTest: v.isFinalTest,
                    modelMeta: v.modelMeta,
                    attempts: 0,
                    evaluation: {questions: []},
                    upload: {
                      archive: null,
                      requestNewRevision: true,
                      revisions: [],
                    },
                    grade: null,
                    maxGrade: v.maxGrade || 10,
                    gradeType: v.gradeType || 'standard',
                    gradeHistory: [],
                    chapter: v.chapter,
                  };

                  if (v.dateStart && v.dateEnd) {
                    Object.assign(data, {
                      dateStart: v.dateStart,
                      dateEnd: v.dateEnd,
                    });
                  }

                  return data;
                }),
                _coursewares: enrolmentDiscipline.coursewares.map(v => v.coursewareId),
                chapter: enrolmentDiscipline.chapter,
              };


              const existDispensation = disciplinesWIthDispensation.find(item =>
                ((item || {}).discId || 'a').toString() === ((objDiscipline || {}).discId || 'b').toString() ||
                item.name === (objDiscipline._name || objDiscipline.name),
              );

              const user = UserValidator.userValidate(req);

              if (existDispensation) {
                Object.assign(
                  objDiscipline,
                  {
                    gradeType: existDispensation.dispensationTypes === 'exploitation' ? 'exploitation' : 'dispensation',
                    grade: existDispensation.grade,
                    status: 'approved',
                    gradeHistory: [
                      {
                        _userName: user._userName,
                        _userId: new Types.ObjectId(user._userId),
                        grade: existDispensation.grade,
                        gradeType: existDispensation.dispensationTypes === 'exploitation' ? 'exploitation' : 'dispensation',
                        status: 'approved',
                        reason: 'Dispensa/Aproveitamento automático (enturmamento).',
                        launchedAt: moment().toDate(),
                      },
                    ],
                  },
                );
              }

              disciplines.push(objDiscipline);
            }

            const set = {
              'registryCourse.course.disciplines': disciplines,
              'registryCourse.course.optioonalDisciplines': optionalDisciplines,
              'registryCourse.course.evaluationMethod': classe.evaluationMethod,
            };

            const objEnrolment = await Enrolments.findById(new Types.ObjectId(selectiveProcessStudentCourse.enrolmentId));

            if (classe.minimalHoursComplementaryActivity) {
              if (!objEnrolment.pillars.find(item => item.name === 'Atividade Complementar')) objEnrolment.pillars.push({
                name: 'Atividade Complementar',
                isCompleted: false,
              });
              Object.assign(
                set,
                {
                  'registryCourse.course.hoursComplementaryActivityRequired': classe.minimalHoursComplementaryActivity,
                  'registryCourse.course.isRequiredHoursComplementaryActivity': true,
                  pillars: objEnrolment.pillars,
                },
              );
            }

            const reEnrolments = (JSON.parse(JSON.stringify(classe.reEnrolments)) || []).map(r => {
              if (!r.term) r.term = {};
              r.wasBlocked = false;
              Object.assign(r.term, {
                wasAccepted: !!((r.term || {}).wasAccepted),
                isRequired: !!r.termReEnrolment,
              });

              return r;
            });

            Object.assign(set, {
              'registryCourse.course.class': {
                _id: classe._id,
                name: classe.name,
                closeDiary: classe.closeDiary,
                evaluationMethod: classe.evaluationMethod,
                reEnrolments: reEnrolments,
              },
            });

            const enrolment = await Enrolments.findOneAndUpdate({_id: selectiveProcessStudentCourse.enrolmentId}, {
              $set: set,
            });

            const user = UserValidator.userValidate(req);

            await EnrolmentNotes.create([
              {
                _enrolmentId: enrolment._id,
                _userId: user._userId,
                _userName: user._userName,
                description: `<p>Usuário ${user._userName} teve enturmamento automatico para turma ${classe.name.trim()}.</p>`,
              },
            ]);

            const disciplinesHadDispensation = enrolment.registryCourse.course.disciplines.filter(item => ['exploitation', 'dispensation'].includes(item.gradeType)).map(objDiscipline => {
              const existDispensation = (disciplinesWIthDispensation || []).find(item =>
                ((item || {}).discId || 'a').toString() === ((objDiscipline || {}).discId || 'b').toString() ||
                item.name === (objDiscipline._name || objDiscipline.name),
              );

              if (existDispensation) return existDispensation;

              return null;
            }).filter(item => !!item);

            // Gera as cobranças antes para que o servico abaixo faça a criacao de forma correta
            const approveService = new ApproveService(req);
            await approveService.createChargesAndContracts(selectiveProcessStudent, selectiveProcessStudentCourse);

            if ((disciplinesHadDispensation || []).length && dispensationSolicitation.length) {
              const lastDispensation = dispensationSolicitation[dispensationSolicitation.length - 1];
              const requestBody = {
                _enrolmentId: lastDispensation._enrolmentId,
                status: lastDispensation.status,
                observation: '**Recalculo de desconto de aproveitamento/dispensa no processo de enturmamento.',
                disciplines: enrolment.registryCourse.course.disciplines.filter(item => ['exploitation', 'dispensation'].includes(item.gradeType)).map(objDiscipline => {
                  const existDispensation = disciplinesWIthDispensation.find(item =>
                    ((item || {}).discId || 'a').toString() === ((objDiscipline || {}).discId || 'b').toString() ||
                    item.name === (objDiscipline._name || objDiscipline.name),
                  );

                  if (existDispensation) {
                    Reflect.deleteProperty(existDispensation, '_id');
                    Reflect.deleteProperty(existDispensation, 'dispensationTypes');
                    return existDispensation;
                  }

                  return null;
                }).filter(item => !!item),
                dispensationTypes: lastDispensation.dispensationTypes,
              };

              await new ApiRequestService(req.models.$company).put(
                'students',
                `dispensation_solicitations/${lastDispensation._id}`,
                requestBody,
                {},
                req.header,
              );
            }


            await Classes.updateOne({_id: classe._id}, {$inc: {vacancies: -1}});

            const classEntry = await EnrolmentClassEntry.findOne({
              _enrolmentId: selectiveProcessStudentCourse.enrolmentId,
            });

            const classEntryData = {
              _enrolmentId: selectiveProcessStudentCourse.enrolmentId,
              _studentCpf: selectiveProcessStudent.student.cpf,
              _studentName: selectiveProcessStudent.student.name,
              _courseId: selectiveProcessStudentsEvaluation._courseId,
              _courseName: selectiveProcessStudentCourse.name,
              _typeName: enrolment.registryCourse.course._typeName,
              _subCategory: enrolment.registryCourse.course._subCategory,
              workload: enrolment.registryCourse.course.workload,
              acronym: enrolment.registryCourse.course.acronym,
              _certifierName: selectiveProcess.certifier.name,
              class: {
                _id: classe._id,
                name: classe.name,
              },
              polo: selectiveProcessStudentCourse.polo,
              selectiveProcess: {
                _selectiveProcessId: selectiveProcess._id,
                _selectiveProcessStudentsId: selectiveProcessStudentsEvaluation._id,
                _selectiveProcessCourseId: selectiveProcessStudentsEvaluation._courseId,
              },
              approved: true,
            };

            if (!classEntry) {
              await EnrolmentClassEntry.create(classEntryData);
            } else {
              await EnrolmentClassEntry.findOneAndUpdate({
                _enrolmentId: selectiveProcessStudentCourse.enrolmentId,
              }, {
                $set: classEntryData,
              },
                {
                  new: true,
                });
            }

            // fim if class
          }
        }
      }
    }

    //Entra nessa condicão se o aluno for aprovado, caso seja aprovado dentro do modulo deo processo seletivo, por la irá gerar essas cobranças.
    if (!classe && updateSelectiveProcessStudentCourse.status === 'approved') {
      // Gera as cobranças antes para que o servico abaixo faça a criacao de forma correta
      const approveService = new ApproveService(req);
      await approveService.createChargesAndContracts(selectiveProcessStudent, selectiveProcessStudentCourse);
    }

    const $set = {
      'courses.$.quiz': updateSelectiveProcessStudentCourse.quiz,
      'courses.$.inputMethod.finished': true,
      'courses.$.documentsSent': updateSelectiveProcessStudentCourse.documentsSent,
    };

    if (updateSelectiveProcessStudentCourse.status)
      $set['courses.$.status'] = updateSelectiveProcessStudentCourse.status;

    // Atualizar SelectiveProcessStudents
    await SelectiveProcessStudents.updateOne({
      'selectiveProcess._id': selectiveProcessStudentsEvaluation._processSelectiveId,
      'student._id': selectiveProcessStudentsEvaluation._studentId,
      'courses.courseId': selectiveProcessStudentsEvaluation._courseId,
    }, {$set});

    if (updateSelectiveProcessStudentCourse.status && updateSelectiveProcessStudentCourse.status === 'approved')
      await EnrolmentClassEntry.updateOne({
        '_enrolmentId': new Types.ObjectId(selectiveProcessStudentCourse.enrolmentId),
      }, {
        $set: {
          approved: true,
        },
      });

    return res.api.send(!!selectiveProcessStudentsEvaluation, res.api.codes.OK);
  } catch (err) {
    console.error(err);
    return res.api.send(`${err}`, res.api.codes.INTERNAL_SERVER_ERROR);
  }
};
