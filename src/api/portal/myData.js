import {Types} from "mongoose";

export default async (req, res) => {
  const {Students, SelectiveProcessStudents} = req.dbs.database_piaget.models;

  const {studentId} = req.tokenPayload;

  try {
    const student = await Students.findOne({
      _id: new Types.ObjectId(studentId)
    });

    const lastSelectiveProcessStudent = await SelectiveProcessStudents.findOne({
      'student._id': new Types.ObjectId(studentId),
    }).sort({createdAt: -1});

    return res.api.send(Object.assign(student, {
      email: lastSelectiveProcessStudent.email,
      birthDate: lastSelectiveProcessStudent.student.birthdate,
    }), res.api.codes.OK);
  } catch (err) {
    console.log(err);
    return res.api.send(`${err}`, res.api.codes.INTERNAL_SERVER_ERROR);
  }
}
