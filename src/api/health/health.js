import mongoose from 'mongoose';

const health = async (req, res) => {

    const healthStatus = results => {

        const hasDownService = Object.keys(results).find(testName => results[testName] === 'down');
        return hasDownService ? 503 : 200;

    };

    let results = {
        mem_cache: 'up'
    };

    Object.keys(mongoose.companies).forEach(
        company => {
            Object.keys(mongoose.companies[company]).forEach(
                db => {
                    if (db && typeof db === 'string' && db.length > 0 && db[0] !== '$') {
                        results[`db_${company}_${db}`] = mongoose.companies[company][db]._readyState === 1 ? 'up' : 'down';
                    }
                }
            );
        }
    );

    res.writeHead(healthStatus(results));
    return res.end(JSON.stringify(results));

};
export default health;
