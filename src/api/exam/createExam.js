import ExamService from '../../services/EXAM/exam.service';

import ActiveCampaignService from '../../services/ActiveCampaign.service';
import moment from "moment";

export default async (req, res) => {
    if(req.body.student.birthDate)
      req.body.student.birthDate = moment(req.body.student.birthDate, 'DD/MM/YYYY').startOf('day').toDate();

    const examService = new ExamService(req);
    const {Exams} = req.dbs.database_leads.models;
    try {
        if (req.dbs.$company != 'prominas') return res.api.send('ok', res.api.codes.CREATED);

        const result = await Exams.create(req.body);
        if (result.activeCampaign) {
            const ac = new ActiveCampaignService();
            await ac.sendData(result);
        }
        const emailTemplates = examService.getEmailTemplates(result.local);

        examService.sendEmail(result.student, {
            template: emailTemplates.BOAS_VINDAS,
            subject: 'Bem vindo'
        });

        return res.api.send(result, res.api.codes.CREATED);
    } catch(err) {
        console.log(err);
        return res.api.send("Erro no Processamento da Validação", res.api.codes.INTERNAL_SERVER_ERROR, {err: err.stack});
    }
}
