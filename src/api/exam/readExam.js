import {Types} from 'mongoose';

export default async (req, res) => {
    const {Exams} = req.dbs.database_leads.models;
    try {
        const data = await Exams.find({'_leadId': new Types.ObjectId(req.params._leadId)}).sort({createdAt: -1});

        return res.api.send(data, res.api.codes.OK);
    } catch (err) {
        console.log(err);
        return res.api.send(`Erro ao atualizar Lead: ${err}`, res.api.codes.INTERNAL_SERVER_ERROR);
    }
}
