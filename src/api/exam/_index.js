import createExamValidate from "./_validates/createExam.validate"

import createExam from "./createExam"
import readExam from "./readExam";
import checkExam from "./checkExam";
import checkExamValidate from "./_validates/checkExam.validate";
import updateExam from "./updateExam";

export default (route) => {
    const resource = '/exam';
    
    // Rota para realizar inscricoes do vestibular
    route.post(`${resource}`, [
        createExamValidate,
        createExam
    ]);

    // Rota para verificar se aluno passou no vestibular ou não
    route.put(`${resource}/:_id`, [
        createExamValidate,
        updateExam
    ]);

    // Rota para verificar se aluno passou no vestibular ou não
    route.put(`${resource}/check/:_id`, [
        checkExamValidate,
        checkExam
    ]);


    // Rota para realizar ler dados de um aluno
    route.get(`${resource}/:_leadId`, [
        readExam
    ]);
}