import {Types} from 'mongoose';
import ExamService from '../../services/EXAM/exam.service';

export default async (req, res) => {
    const examService = new ExamService(req);
    const {Exams, Leads} = req.dbs.database_leads.models;
    try {
        const insertQuery = examService.checkGrade(req.body);
        const result = await Exams.findOneAndUpdate(
            {_id: new Types.ObjectId(req.params._id)}, 
            {$set: insertQuery},
            {$new: true}
        );
        let data = null;
        let status = null;

        if(req.body.approved) {
            status = 'in_progress'
            data = {
                giveUp: false,
                _activityName: 'Aprovado no Vestibular',
                type: 'Telefone',
                observation: 'Aprovado no vestibular através da Central de Leads',
                observationImage: '',
                brokerName: req.body.brokerName,
                _brokerId: req.body._brokerId
            };
        } else {
            status = 'finished'
            data = {
                giveUp: false,
                _activityName: 'Reprovado no Vestibular',
                type: 'Telefone',
                observation: 'Reprovado no vestibular através da Central de Leads',
                observationImage: '',
                brokerName: req.body.brokerName,
                _brokerId: req.body._brokerId
            }
        }

        await Leads.findOneAndUpdate(
            {_id: result._leadId},
            {
                $set: {status},
                $push: {
                    activities: data
                }
            }, {new: true});

        return res.api.send(result, res.api.codes.CREATED);
    } catch(err) {
        console.log(err);
        return res.api.send(`Erro ao atualizar Lead: ${err}`, res.api.codes.INTERNAL_SERVER_ERROR);
    }
}
