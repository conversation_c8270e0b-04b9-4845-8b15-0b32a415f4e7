import {Types} from 'mongoose';
import moment from 'moment';
import ActiveCampaignService from '../../services/ActiveCampaign.service';
import ExamService from '../../services/EXAM/exam.service';

export default async (req, res) => {
    const examService = new ExamService(req);
    const {Exams} = req.dbs.database_leads.models;

    if (req.dbs.$company != 'prominas') return res.api.send('ok', res.api.codes.OK);
    try{

        //Check Data de nascimento
        if (req.body.student && req.body.student.birthDate) {
            req.body.student.birthDate = moment(req.body.student.birthDate, 'DD/MM/YYYY').startOf('day').toDate();
        }

        const data = await Exams.findOneAndUpdate({_id: new Types.ObjectId(req.params._id)}, {$set: req.body}, {new: true});
        if (data.activeCampaign) {
            const ac = new ActiveCampaignService();
            await ac.sendData(data);
        }
        const emailTemplates = examService.getEmailTemplates(data.local);

        if (data.student.cpf) {

            if (data.type === 'enem' && data.enem && data.enem.grade) {
                if (data.enem.grade >= 450) {
                    const msg = encodeURIComponent(`Olá! Meu nome é ${data.student.name} e fui aprovado no Vestibular do curso de ${data.course.name}. Quero fazer a minha matrícula. Pode me ajudar?`);

                    examService.sendEmail(data.student, {
                        template: emailTemplates.RESULTADO,
                        subject: 'Não precisa ficar mais ansioso, seu resultado chegou! 😄👏',
                        link: `https://api.whatsapp.com/send?phone=5531994290133&text=${msg}`
                    });
                } else {
                    examService.sendEmail(data.student, {
                        template: emailTemplates.ENEM,
                        subject: 'Estamos analisando o seu resultado, já já te enviaremos a resposta!'
                    });
                }
            }

            if (data.type === 'dissertation' ) {
                examService.sendEmail(data.student, {
                    template: emailTemplates.REDACAO,
                    subject: 'Acabamos de receber sua redação, em breve te enviamos o resultado, tá?!'
                });
            }

        }

        return res.api.send(data, res.api.codes.OK);

    } catch (err) {
        console.log(err);
        return res.api.send(`Erro ao atualizar Lead: ${err}`, res.api.codes.INTERNAL_SERVER_ERROR);
    }
}
