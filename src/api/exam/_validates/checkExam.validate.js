/* eslint-disable array-element-newline */
import <PERSON><PERSON> from 'joi';

export default (req, res, next) => {
    return Joi.object({
        approved: Joi.boolean(),
        _brokerId        : Joi.string().regex(Joi.regexes.objectId).required(),
        brokerName       : Joi.string(),
        dissertation     : Joi.object({
            grade: Joi.number().min(0).max(100)
        })
    }).validate(req.body, (err) => {
        if (err)
            return res.api.send(
                err.details,
                res.api.codes.UNPROCESSABLE_ENTITY
            );

        return next();
    });
};
