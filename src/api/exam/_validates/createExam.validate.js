/* eslint-disable array-element-newline */
import <PERSON><PERSON> from 'joi';

export default (req, res, next) => {
    return Joi.object({
        type: Joi.string(),
        enem: {
            year: Joi.number(),
            grade: Joi.number().min(0).max(1000),
            link: Joi.string(),
        },
        dissertation: {
            theme: Joi.string(),
            link: Joi.string(),
        },
        student: {
            name: Joi.string().required(),
            email: Joi.string().email().required(),
            phone: Joi.string().required(),
            cpf: Joi.string(),
            birthDate: Joi.string(),
            address: {
                street: Joi.string(),
                zone: Joi.string(),
                number: Joi.string(),
                complement: Joi.string().allow(null),
                zip: Joi.string(),
                city: Joi.string(),
                uf: Joi.string()
            }
        },
        course: {
            name: Joi.string().required(),
            type: Joi.string().required()
        },
        modality: Joi.string(),
        local: {
            type: Joi.string().required(),
            name: Joi.string()
        },
        params: {
            name: Joi.string(),
            email: Joi.string(),
            phone: Joi.string(),
            modality: Joi.string(),
            poloCity: Joi.string(),
            course: Joi.string(),
            utm_source: Joi.string(),
            utm_campaign: Joi.string(),
            utm_medium: Joi.string(),
            utm_term: Joi.string(),
            utm_content: Joi.string(),
            gclid: Joi.string(),
            fbclid: Joi.string()
        },
        _leadId: Joi.string().required(),
        activeCampaign: Joi.boolean().optional().allow(null),
        step: Joi.string().optional().allow(['', null]),
        listId: Joi.string().optional().allow(['', null]),
        metadata : Joi.object()
    }).validate(req.body, (err) => {
        if (err) {
            return res.api.send(
                err.details,
                res.api.codes.UNPROCESSABLE_ENTITY
            );
        }

        return next();
    });
};
