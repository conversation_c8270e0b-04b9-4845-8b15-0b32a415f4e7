/**
 * Find register of SelectiveProcess by id
 */
const get = (req, res) => {
    const {SelectiveProcess} = req.dbs.database_piaget.models;
    return SelectiveProcess
        .findById(req.params._id, req.query.project)
        .populate(req.query.populate)
        .then(forum => {
            if (!forum) return res.api.send(null, res.api.codes.NOT_FOUND);

            return res.api.send(forum, res.api.codes.OK);
        })
        .catch(err => {
            return res.api.send(err, res.api.codes.INTERNAL_SERVER_ERROR);
        })
};

export default get;
