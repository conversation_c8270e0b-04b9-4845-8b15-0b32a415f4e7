import NofifyService from '../../services/NotifyService';

/**
 * Update register of SelectiveProcess
 */
const update = (req, res) => {
    const {SelectiveProcess} = req.dbs.database_piaget.models;
    return SelectiveProcess
        .findOneAndUpdate(
            {_id: req.params._id},
            {$set: req.body},
            {new: true}
        )
        .then(async updated => {
            if (!updated) return res.api.send(null, res.api.codes.NOT_FOUND);

            if ('archive' in req.body) {
                const nofifyService = new NofifyService(req, updated);
                await nofifyService.send();
            }

            return res.api.send(updated, res.api.codes.OK);
        })
        .catch(err => {
            return res.api.send(err, res.api.codes.INTERNAL_SERVER_ERROR);
        });
};

export default update;
