import { Types } from "mongoose";
import moment from 'moment';

const asyncForEach = async (array, callback) => {
    for (let index = 0; index < array.length; index++) {
        await callback(array[index], index, array);
    }
};

const clone = async (req, res) => {
    try {
        const {SelectiveProcess} = req.dbs.database_piaget.models;
        const selectiveProcess = await SelectiveProcess.findOne(
            {
                _id: new Types.ObjectId(req.params._id)
            },
            {
                _id: 0,
                createdAt: 0,
                updatedAt: 0,
                _v: 0,
                __v:0
            }
        ).lean();

        const cloneArray = [];

        for (let i = 0; i < req.body.qtdCopy; i++) {
            cloneArray.push(i)
        }

        await asyncForEach(cloneArray, async (data, index) => {
            const copySelectiveProcess = JSON.parse(JSON.stringify(selectiveProcess));
            copySelectiveProcess.name = selectiveProcess.name + ` Clone(${index + 1}) de ` + moment().format('DD/MM/Y HH:mm:ss');
            copySelectiveProcess.dateStart = req.body.dateStartNew;
            copySelectiveProcess.dateEnd = req.body.dateEndNew;

            await SelectiveProcess.create(copySelectiveProcess);
        });


        return res.api.send('clonado com sucesso', res.api.codes.OK);
    } catch (err) {
        console.log(err);

        return res.api.send(err.stack, res.api.codes.INTERNAL_SERVER_ERROR);
    }
};

export default clone;
