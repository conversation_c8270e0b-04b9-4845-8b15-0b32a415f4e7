/**
 * Create new SelectiveProcess by req.body data
 */

const create = (req, res) => {
    const {SelectiveProcess} = req.dbs.database_piaget.models;
    return SelectiveProcess
        .create(req.body)
        .then(forum => {
            return res.api.send(forum, res.api.codes.CREATED);
        })
        .catch(err => {
            return res.api.send(err.stack, res.api.codes.INTERNAL_SERVER_ERROR);
        })
};

export default create;
