import moment from 'moment';
import {Types} from 'mongoose';

let SelectiveProcess;
let SelectiveProcessStudents;
//et company;

/**
 * Find all registers of SelectiveProcess collection
 */
const list = async (req, res) => {
  SelectiveProcess = req.dbs.database_piaget.models.SelectiveProcess;
  SelectiveProcessStudents = req.dbs.database_piaget.models.SelectiveProcessStudents;
 // company = req.dbs.$company;
  const certifierId = (req.query || {}).certifierId;
  const selectiveProcessId = req.query.selectiveProcessId;

  const selectiveProcess = await getDataSelectiveProcess(selectiveProcessId, certifierId);
  if (selectiveProcess.error) {
    return res.api.send(selectiveProcess.error.message, res.api.codes[selectiveProcess.error.code]);
  }

  //caso nao existir polo setado no processo seletivo, setar todos os polos disponiveis
  if (selectiveProcess[0].polos.length === 0) {
    selectiveProcess[0].allPolos = true;
    selectiveProcess[0].polos = await getPolos(req);
  }

  return res.api.send(selectiveProcess[0], res.api.codes.OK);
};

//buscar dados do processo seletivo como curso, polos e outros.
const getDataSelectiveProcess = async (selectiveProcessId, certifierId) => {
  const query = [
    {
      $match: {
        _id: new Types.ObjectId(selectiveProcessId),
        'certifier._id': new Types.ObjectId(certifierId),
        isActive: true,
        // dateStart: {$lte: new Date()},
        // dateEnd: {$gte: new Date()}
      }
    },
    {
      '$unwind': {
        'path': '$courses'
      }
    },
    {
      '$lookup': {
        'from': 'Courses',
        'localField': 'courses.courseId',
        'foreignField': '_id',
        'as': 'originalCourse'
      }
    },
    // {
    //     $match: {
    //         'originalCourse.isEnabled': true
    //     }
    // },
    {
      '$group': {
        _id: '$_id',
        courses: {
          "$push": {
            '_id': '$_id',
            'name': '$courses.name',
            'courseArrayId': '$courses._id',
            'courseId': '$courses.courseId',
            'courseType': '$courses.courseTypeName',
            'vacancies': '$courses.vacancies',
            'modalityEnrolments': '$courses.modalityEnrolments',
            'selectiveProcessFeeType': '$courses.selectiveProcessFeeType',
            'selectiveProcessFeeAmount': '$courses.selectiveProcessFeeAmount',
            'chargeEnrolmentsfee': '$courses.chargeEnrolmentsfee',
            'rateEnrolmentFeeAmount': '$courses.rateEnrolmentFeeAmount',
            dateStart: '$dateStart',
            dateEnd: '$dateEnd',
            courseIsEnabled: '$originalCourse.isEnabled',
            needHealthAreaPolo: '$originalCourse.needHealthAreaPolo',
          },
        },
        polos: {
          '$first': '$polos'
        },
        originalCourse: {$addToSet: {'arrayCourses': '$originalCourse'}}
      }
    }
  ]

  let selectiveProcess = await SelectiveProcess.aggregate(
    query
  ).then(
    async result => {
      if (!(result && Array.isArray(result) && result.length)) {
        return null;
      }

      result = result.map(item => ({
        ...item,
        courses: item.courses.map(course => {
          const originalCourse = (((item || {}).originalCourse || [{}])[0].arrayCourses || []).find(oc => (course.courseId || 'a').toString() === (oc._id || 'b').toString());

          return Object.assign(course, {
            name: originalCourse ? `${(originalCourse || {})._name} - ${(originalCourse || {}).workload}h` : course.name,
            originalName: course.name.replace(/\s+\d+H.+$/, ''), // remove carga horaria e acronimo: CURSO X 30H - ABC123 -> CURSO X
          })
        }),
      }));

      return result;
    }
  ).catch(
    async err => {
      throw new Error(err);
    }
  );

  if (!selectiveProcess) {
    return {
      error: {
        message: `Este vestibular não foi encontrado.`,
        code: 'NOT_FOUND'
      }
    }
  }

  if (!selectiveProcess[0].courses || selectiveProcess[0].courses.find(course => !course.courseIsEnabled[0])) {
    return {
      error: {
        message: `Este vestibular não possuí cursos ativos, Favor entrar em contato com a instituição.`,
        code: 'NOT_FOUND'
      }
    }
  }

  if (moment().isBefore(moment(selectiveProcess[0].courses[0].dateStart).startOf('day').toDate())) {
    return {
      error: {
        message: `Vestibular ainda não está vigente! ele será iniciado na data ${moment(selectiveProcess[0].courses[0].dateStart).format('DD/MM/YYYY')}`,
        code: 'NOT_FOUND'
      }
    }
  }

  if (moment().isAfter(moment(selectiveProcess[0].courses[0].dateEnd).endOf('day').toDate())) {
    return {
      error: {
        message: `Este vestibular não está mais vigente! Sua data de vigência foi ${moment(selectiveProcess[0].courses[0].dateStart).format('DD/MM/YYYY')} até ${moment(selectiveProcess[0].courses[0].dateEnd).format('DD/MM/YYYY')}.`,
        code: 'NOT_FOUND'
      }
    }
  }

  let selectiveProcessStudents = await getSelectiveProcessStudents(selectiveProcessId)

  if (!(selectiveProcess && Array.isArray(selectiveProcess) && selectiveProcess.length)) {
    return {
      error: {
        message: 'Não existe vestibular disponível!',
        code: 'NOT_FOUND'
      }
    };
  }

  if (selectiveProcess && selectiveProcess[0].courses) {
    selectiveProcess[0].courses.forEach(course => {
      course.students = 0;
      course.vacanciesLeft = course.students >= course.vacancies ? 0 : course.vacancies - course.students;
    });
    if (selectiveProcessStudents.length > 0) {
      selectiveProcess[0].courses = selectiveProcess[0].courses.map(course => {
        const students = selectiveProcessStudents.find(result => {
          return result._id.toString() === course.courseId.toString();
        });
        if (students) {
          course.students = students.count;
        }
        course.vacanciesLeft = course.students >= course.vacancies ? 0 : course.vacancies - course.students;
        return course;
      });
    }
  }
  return selectiveProcess;

};

//Busca todos os polos
const getPolos = async (req) => {
  const $match = {

  };

  if (
    'hasSuportToHealth' in req.query &&
    req.query.hasSuportToHealth !== '' &&
    req.query.hasSuportToHealth !== 'null'
  )
    $match['hasSuportToHealth'] = req.query.hasSuportToHealth === 'true';

  if('state' in req.query && req.query.state !== '' && req.query.state !== 'null')
    $match['addresses.state'] = req.query.state;

  if('city' in req.query && req.query.city !== '' && req.query.city !== 'null')
    $match['addresses.city'] = req.query.city;

  return await req.dbs.database_piaget.models.Polos.aggregate([
    {$match}, {
      $project: {
        _id: '$_id',
        name: '$name',
      }
    }
  ]).then(async polos => {
    if (!polos) {
      return [];
    }
    return polos;
  }).catch(async () => {
    throw Error('Polos não encontrados.');
  });
};

//Buscar todas matricular no processo seletivo
const getSelectiveProcessStudents = async (selectiveProcessId) => {
  return await SelectiveProcessStudents.aggregate(
    [
      {
        '$match': {
          'selectiveProcess._id': new Types.ObjectId(selectiveProcessId),
        },
      }, {
      '$unwind': {
        path: '$courses',
      },
    }, {
      '$group': {
        _id: "$courses.courseId",
        count: {$sum: 1}
      }
    }
    ]
  ).then(
    async data => {
      return data;
    }
  ).catch(
    async () => {
      return {
        error: {
          message: 'Ocorreu um erro para buscar a quantidade de matriculas no processo seletivo, entre em contato com o suporte!',
          code: 'INTERNAL_SERVER_ERROR'
        }
      };
    }
  );
}

export default list;
