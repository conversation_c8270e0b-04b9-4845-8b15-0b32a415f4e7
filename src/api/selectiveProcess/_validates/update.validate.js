
import Joi from 'joi';
// .when('content', {is: '', then: Joi.string().required()})
export default (req, res, next) => {

    return Joi
        .object(
            {
                name: Joi.string().required(),

                cover: Joi.string().uri(),

                certifier: Joi.object({
                    _id: Joi.string().required(),
                    name: Joi.string().required(),
                }).required(),

                polos: Joi.array().items(Joi.object({
                        _id: Joi.string().optional(),
                        poloId: Joi.string().required(),
                        name: Joi.string().required(),
                    }),
                ),

                courses: Joi.array().items(Joi.object({
                        _id: Joi.string().allow(['', null]),
                        id: Joi.string().allow(['', null]),
                        courseTypeName: Joi.string().required(),
                        courseId: Joi.string().required(),
                        name: Joi.string().required(),
                        vacancies: Joi.number().required(),
                        modalityEnrolments: Joi.array().items(Joi.string().valid('enem', 'transfer', 'proof', 'new_title')),
                        descriptionForProof: Joi.string().required(),
                        types: Joi.array().items(Joi.string().valid('Prova Agendada', 'Prova Online', 'Envio de Documento')),
                        maximumDuration: Joi.number().required(),
                        chargeEnrolmentsfee: Joi.boolean().required(),
                        rateEnrolmentFeeAmount: Joi.number().required(),
                        selectiveProcessFeeType: Joi.string().valid('noCharge', 'enrolmentFee', 'personalized').required(),
                        selectiveProcessFeeAmount: Joi.number().required(),
                        chargeType: Joi.string().allow(['', null]),
                        groupQuestion: Joi.object({
                                _id: Joi.string().optional(),
                                name: Joi.string().allow(null),
                                openQuestion: Joi.number().required(),
                                archiveQuestion: Joi.number().required(),
                                closeQuestion: Joi.number().required(),
                            }
                        ).required(),
                        classes: Joi.array().items(Joi.object({
                                _id: Joi.string().optional().allow(''),
                                id: Joi.string().optional().allow(''),
                                classId: Joi.string().allow(''),
                                name: Joi.string().allow(''),
                                metadata: Joi.object().allow(['', null])
                            })
                        ).required(),
                    })
                ).allow([]),

                description: Joi.string().required(),

                dateStart: Joi.string().required(),

                dateEnd: Joi.string().required(),

                autoCorrection: Joi.boolean().optional(),

                percentageApproval: Joi.number().optional(),

                visibility: Joi.string().valid(['public', 'internal']).optional(),

                situationAfterEnrollment: Joi.array().items(Joi.string()).valid(['pre_register', 'matriculate']).required(),

                email_registration: Joi.string().optional(),

                email_enrollment: Joi.string().optional(),

                isActive: Joi.boolean().required(),
            }
        )
        .validate(req.body, err => {
            if (err)
                return res.api.send(err.details, res.api.codes.UNPROCESSABLE_ENTITY);

            return next();
        });
}
