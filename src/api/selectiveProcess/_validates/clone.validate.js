import Joi from 'joi';

export default (req, res, next) => {
    return Joi
        .object(
            {
                name: Joi.string().required(),
                qtdCopy: Joi.number().required(),
                dateStart: Joi.date().required(),
                dateEnd: Joi.date().required(),
                dateStartNew: Joi.date().required(),
                dateEndNew: Joi.date().required()
            }
        )
        .validate(req.body, err => {
            if (err)
                return res.api.send(err.details, res.api.codes.UNPROCESSABLE_ENTITY);

            return next();
        });
}
