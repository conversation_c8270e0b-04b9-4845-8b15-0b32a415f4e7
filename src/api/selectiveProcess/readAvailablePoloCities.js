/* eslint-disable no-undefined,no-confusing-arrow */
/**
 * Find register of SelectiveProcess by id
 */
const get = (req, res) => {
  const {Polos} = req.dbs.database_piaget.models;

  return Polos
    .find({
      isActive: true,
      'addresses.state': req.params.uf
    }, {'addresses.city': 1})
    .then(polos => {
      const cities = polos
        .map(polo => polo.addresses ? polo.addresses.city : undefined)
        .filter(polo => !!polo);

      return res.api.send(Array.from(new Set(cities)), res.api.codes.OK);
    })
    .catch(err => {
      return res.api.send(err, res.api.codes.INTERNAL_SERVER_ERROR);
    })
};

export default get;
