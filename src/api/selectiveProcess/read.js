/**
 * Find all registers of SelectiveProcess collection
 */
const list = (req, res) => {
    const { SelectiveProcess } = req.dbs.database_piaget.models;

    const aggregate = req.query.aggregate;

    return SelectiveProcess
        .paginate(aggregate, req.query.limit, req.query.page)
        .then(result => {
            if (!result.data.length) return res.api.send(null, res.api.codes.NOT_FOUND);

            return res.api.send(result.data, res.api.codes.OK, {paginate: result.paginate});
        })
        .catch(err => {
            console.log(err);

            return res.api.send(err.stack, res.api.codes.INTERNAL_SERVER_ERROR);
        });
};

export default list;
