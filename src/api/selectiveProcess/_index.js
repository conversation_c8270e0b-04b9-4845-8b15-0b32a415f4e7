import createValidate from './_validates/create.validate'
import updateValidate from './_validates/update.validate'
import cloneValidate from './_validates/clone.validate';

import create from './create';
import readOne from './readOne';
import read from './read';
import readData from './readData';
import update from './update';
import readAvailablePoloStates from './readAvailablePoloStates';
import readAvailablePoloCities from "./readAvailablePoloCities";
import clone from './clone';

export default (route) => {
  const resource = '/selective-process';

  // Rota para criar vestibular
  route.post(`${resource}`, [
    createValidate,
    create
  ]);

  // Rota para atualizar vestibular
  route.put(`${resource}/:_id`, [
    updateValidate,
    update
  ]);

  // Rota para buscar todos os vestibulares
  route.get(`${resource}`, read);

  // Rota para buscar todos os vestibulares ativos e vigentes
  route.get(`${resource}/data`, readData);

  // Rota para listar estados disponiveis (de acordo com os polos disponiveis)
  route.get(`${resource}/available-polo-states`, readAvailablePoloStates);

  // Rota para listar cidades disponiveis (de acordo com estado selecionado e os polos disponiveis)
  route.get(`${resource}/available-polo-cities/:uf`, readAvailablePoloCities);

  // Rota para buscar um vestibular
  route.get(`${resource}/:_id`, readOne);

  route.post(`${resource}/clone/:_id`, [
    cloneValidate,
    clone
  ]);
}
