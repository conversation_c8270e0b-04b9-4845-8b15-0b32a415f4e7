import request from 'request-promise';
import ApiConfig from "../../config/api.conf";
const config = new ApiConfig();
const environment = config.getEnv();

export default async (req, res) => {
    try {
        const url = `https://www.google.com/recaptcha/api/siteverify?secret=${environment.app.recapcha}&response=${req.params.token}`;

        const sent = await request({
            uri: url,
            method: 'POST',
            json  : true
        });

        return res.api.send(sent, res.api.codes.OK);
    } catch (err) {
        console.log(err);
        return res.api.send(err, res.api.codes.CONFLICT);
    }
}
