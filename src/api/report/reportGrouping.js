import SpreadsheetService from '../../services/SPREADSHEET/Spreadsheet.service';
let EnrolmentClassEntry;

/**
 * Find all registers of SelectiveProcessStudents collection
 */

const _generateXlsx = async (data) => {
    const spreadsheetService = new SpreadsheetService();
    const columns = [
        {name: '<PERSON><PERSON>'},
        {name: 'CP<PERSON>'},
        {name: '<PERSON><PERSON><PERSON>'},
        {name: 'Tipo de curso'},
        {name: 'Cert<PERSON>ador<PERSON>'},
        {name: 'Vestibular inscrito'},
        {name: '<PERSON>'},
        {name: 'Email'}
    ];

    spreadsheetService.createWorksheet(
        'Listagem Enturmamento',
        columns,
        data.map(item => [
            item.name,
            item.cpf,
            item.courseName,
            item.courseType,
            item.certifier,
            item.selectiveProcess,
            item.polo,
            item.email
        ])
    );

    return spreadsheetService.generateTableBuffer();
};


const reportGrouping = (req, res) => {
    EnrolmentClassEntry = req.dbs.database_piaget.models.EnrolmentClassEntry;
    return EnrolmentClassEntry
        .aggregate(req.query.aggregate)
        .then(async result => {
            let buffer =  await _generateXlsx(result);
            res.setHeader(
                'Content-Type',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            );
            res.setHeader(
                'Content-Disposition',
                'attachment; filename=teste.xlsx'
            );
            return res.send(buffer);
        })
        .catch(err => {
            return res.api.send(err, res.api.codes.INTERNAL_SERVER_ERROR);
        });
};

export default reportGrouping;
