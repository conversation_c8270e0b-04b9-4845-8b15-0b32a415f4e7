export default async (req, res) => {
    const {Configurations} = req.dbs.database_piaget.models;
    const arrConfigsAllow = ["vestibular_privacy_and_policy", 'hidden_payment_plan_in_vestibular', 'barema'];

    if (!arrConfigsAllow.includes(((req.params || {}).name || '').toString().toLowerCase())) {
        console.log('Config', ((req.params || {}).name || '').toString().toLowerCase());
        return res.api.send(`Não é permitido buscar essa configuração!`, res.api.codes.INTERNAL_SERVER_ERROR);
    }

    try {
        const data = await Configurations.findOne({'name': req.params.name, isActive: true});

        return res.api.send(data, res.api.codes.OK);
    } catch (err) {
        console.log(err);
        return res.api.send(`Erro ao Buscar Configurações: ${err}`, res.api.codes.INTERNAL_SERVER_ERROR);
    }
}
