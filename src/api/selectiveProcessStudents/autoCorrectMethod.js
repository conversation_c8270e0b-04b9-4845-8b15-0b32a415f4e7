import {Types} from 'mongoose';
import moment from "moment";
import {CREATED, INTERNAL_SERVER_ERROR, NOT_FOUND} from "http-status-codes";
import ApproveService from "../../services/Aprove.service";

let SelectiveProcessStudents;
let EnrolmentClassEntry;
let message = 'approved';

const getSelectiveProcesssStudents = (selectiveProcessId = null) => {
    let $match = {
        'autoCorrection': true,
    }

    if (selectiveProcessId) {
        Object.assign($match,
            {'_id': new Types.ObjectId(selectiveProcessId)}
        )
    } else {
        Object.assign($match,
            {
                'dateStart': {$lte: new Date()},
                'dateEnd': {$gte: new Date(moment().subtract(1, 'days'))}
            }
        )
    }

    return SelectiveProcessStudents
        .find($match)
        .then(result => {
            if (!result) return null;
            return result;
        })
        .catch(err => {
            return err;
        });
}

const updateClassEntry = async (selectiveProcessStudentsId, courseId) => {
    return await EnrolmentClassEntry.findOneAndUpdate(
        {
            'selectiveProcess._selectiveProcessStudentsId': new Types.ObjectId(selectiveProcessStudentsId.toString()),
            'selectiveProcess._selectiveProcessCourseId': new Types.ObjectId(courseId.toString())
        },
        {
            $set: {
                approved: true
            }
        },
        {
            new: true
        }
    ).then(
        async r => r
    ).catch(
        async e => {
            console.log(e);
            return null;
        }
    );
}

const correctProof = async (req, selectiveProcessStudents, courseId = null) => {
    const approveService = new ApproveService(req);
    const result = {};
    for (const selectiveProcessStudent of selectiveProcessStudents) {
        const indexSelectiveProcessStudent = selectiveProcessStudents.indexOf(selectiveProcessStudent);

        //the number of questions
        let qtdQuestions = 0;

        selectiveProcessStudent.courses.map(course => {
            let validateMethod = course.inputMethod.method !== 'proof'
            let validateWasFixedAutomatically = course.quiz.wasFixedAutomatically
            let validateCourseId = courseId && courseId.toString() !== course.courseId.toString()

            let dateStart = moment(course.quiz.dateStart)
            let durationMinutes = moment().diff(dateStart, 'minutes')
            let maximumDuration = course.maximumDuration
            let validateDuration = !courseId && durationMinutes <= maximumDuration

            if (validateMethod || validateWasFixedAutomatically || validateCourseId || validateDuration) {
                return course
            }

            let existsTypeText = false;
            let qtdCorrectQuestions = 0;

            //fix questions
            course.quiz.questions.map(question => {
                if (question.typeOfAnswer === 'select') {
                    //fix closed questions
                    const alternative = question.alternatives.find(a => {
                        const responseOption = question.responseOption ? question.responseOption.toString() : null
                        return (a.alternativeId.toString() === responseOption)
                    })

                    //mark the question as correct or incorrect, if marked by the student
                    if (alternative) {
                        question.correct = alternative.correctAlternative
                    } else {
                        //if there is no question, mark it as incorrect
                        question.correct = false
                    }

                    //add the number of correct questions
                    if (question.correct) {
                        qtdCorrectQuestions++;
                    }

                    //add the number of questions
                    qtdQuestions++;
                } else if (!existsTypeText) {
                    //fix open questions
                    existsTypeText = true;
                }
                return question
            });

            //if there are no open questions and there are questions, set the other fields
            if (!existsTypeText && course.quiz.questions.length > 0) {
                course.inputMethod.rated = true; //set as fixed
                course.inputMethod.finished = true; //set as finished
                course.quiz.wasFixedAutomatically = true; //set as auto-fixed

                course.quiz.dateEnd = dateStart.add(maximumDuration, 'minutes') //set end date

                const qtdTotalQuestions = course.quiz.questions.length
                const hitPercentage = 100 * qtdCorrectQuestions / qtdTotalQuestions;
                course.inputMethod.approved = selectiveProcessStudent.percentageApproval <= hitPercentage; //set as approved
                if (!course.charge || (course.charge && course.charge.isPayment)) {
                    course.status = course.inputMethod.approved ? 'approved' : 'disapproved'; //set as approved
                }
                course.quiz.hitPercentage = hitPercentage; //set percentage of hit
            }
            return course;
        });

        for (const course of selectiveProcessStudent.courses) {
            if (course.status === 'approved') {
                try {
                    if (((course || {}).courseId || '').toString() === courseId.toString()) {
                        await updateClassEntry(selectiveProcessStudent._id, courseId);
                        message = await approveService.createChargesAndContracts(selectiveProcessStudent, course);
                    }
                } catch (err) {
                    throw new Error(err);
                }
            }
        }


        if (qtdQuestions > 0) {
            await selectiveProcessStudents[indexSelectiveProcessStudent].save()
                .then(
                    () => {
                        Object.assign(result, {
                            'success': {
                                message: 'Sucesso ao corrigir ' + qtdQuestions + ' questões.',
                                code: CREATED
                            }
                        })
                    }
                )
                .catch(
                    () => {
                        Object.assign(result, {
                            'error': {
                                message: 'Erro ao corrigir uma das questões',
                                code: INTERNAL_SERVER_ERROR
                            }
                        })
                    }
                )
        } else if (!result.success) {
            Object.assign(result, {
                'success': {
                    message: 'Não existe questões para serem corrigidas.',
                    code: NOT_FOUND
                }
            })
        }
    }

    return result;
};

const get = async (req, res) => {
    SelectiveProcessStudents = req.dbs.database_piaget.models.SelectiveProcessStudents;
    EnrolmentClassEntry = req.dbs.database_piaget.models.EnrolmentClassEntry;
    let selectiveProcessStudents = null;
    if (req.params._id) {
        selectiveProcessStudents = await getSelectiveProcesssStudents(req.params._id)
    } else {
        selectiveProcessStudents = await getSelectiveProcesssStudents()
    }

    if (!selectiveProcessStudents || selectiveProcessStudents.length === 0) {
        return res.api.send('Não existe dados para correção!', res.api.codes.NOT_FOUND);
    }

    let result = await correctProof(req, selectiveProcessStudents, req.params.courseId)

    if (!result) {
        return res.api.send('Não foi possivel corrigir seus dados, entre em contato com o suporte!', res.api.codes.INTERNAL_SERVER_ERROR);
    } else if (result.error) {
        return res.api.send(result.error.message, result.error.code);
    }

    return res.api.send(message, res.api.codes.CREATED);
};

export default get;
