import {Types} from 'mongoose';

let SelectiveProcessStudents;

/**
 * Find all registers of SelectiveProcessStudents collection
 */

const getSelectiveProcesssStudent = (selectiveProcessId, courseId) => {
    const query = [
        {
            '$match':
                {
                    '_id': new Types.ObjectId(selectiveProcessId)
                }
        },
        {
            '$unwind': {
                'path': '$courses'
            }
        },
        {
            '$match':
                {
                    'courses.courseId': new Types.ObjectId(courseId)
                }
        }
    ];

    return SelectiveProcessStudents
        .aggregate(query)
        .then(result => {
            if (!result) return null;
            return result[0];
        })
        .catch(err => {
            return err;
        });
}

const replyEnemSelectiveProcesssStudent = (id, courseId, data) => {
    return SelectiveProcessStudents
        .updateOne({"_id": new Types.ObjectId(id), "courses.courseId": new Types.ObjectId(courseId)}, {
            $set:
                {
                    "courses.$.inputMethod.finished": true,
                    'courses.$.enemScore': data.enemScore,
                    'courses.$.documentsSent.message': data.message,
                    'courses.$.documentsSent.files': data.files
                }
        }, {upsert: true})
        .then(result => {
            if (result.n !== result.nModified) return null;
            return result;
        })
        .catch(err => {
            return err;
        });
}

const replyTransferSelectiveProcesssStudent = (id, courseId, data) => {
    return SelectiveProcessStudents
        .updateOne({"_id": new Types.ObjectId(id), "courses.courseId": new Types.ObjectId(courseId)}, {
            $set:
                {
                    "courses.$.inputMethod.finished": true,
                    'courses.$.documentsSent.message': data.message,
                    'courses.$.documentsSent.files': data.files
                }
        }, {upsert: true})
        .then(result => {
            if (result.n !== result.nModified) return null;
            return result;
        })
        .catch(err => {
            return err;
        });
}

const replyNewTitleSelectiveProcesssStudent = (id, courseId, data) => {
    return SelectiveProcessStudents
        .updateOne({"_id": new Types.ObjectId(id), "courses.courseId": new Types.ObjectId(courseId)}, {
            $set:
                {
                    "courses.$.inputMethod.finished": true,
                    'courses.$.documentsSent.message': data.message,
                    'courses.$.documentsSent.files': data.files
                }
        }, {upsert: true})
        .then(result => {
            if (result.n !== result.nModified) return null;
            return result;
        })
        .catch(err => {
            return err;
        });
}

const replyProofSelectiveProcesssStudent = async (id, courseId, body) => {
    const questions = body.questions;
    const isFinished = body.finished;
    const selectiveProcess = await SelectiveProcessStudents.findOne({
        _id: new Types.ObjectId(id),
    })

    selectiveProcess.courses.map(course => {
        if (course.courseId == courseId) {
            let qtdQuestion = 0;
            course.quiz.questions.map(q => {
                const question = questions.find(question => {
                    return question.questionId == q.questionId
                })
                if (question) {
                    if (question.responseText !== null) {
                        qtdQuestion++;
                        q.responseText = question.responseText
                    }
                    if (question.responseOption !== null) {
                        qtdQuestion++;
                        q.responseOption = question.responseOption
                    }
                }
                return q
            })

            if (course.quiz.questions.length === qtdQuestion && !!isFinished) {
                course.inputMethod.finished = true;
            }

            course.inputMethod.spendingTime = (body || {spendingTime: false}).spendingTime || false;

            return course;
        }
    })

    return selectiveProcess.save()
}

const get = async (req, res) => {
    SelectiveProcessStudents = req.dbs.database_piaget.models.SelectiveProcessStudents;
    const selectiveProcessStudent = await getSelectiveProcesssStudent(req.params._id, req.params.courseId)
    let reply = null;

    if (selectiveProcessStudent.courses.inputMethod.method === 'enem') {
        reply = await replyEnemSelectiveProcesssStudent(req.params._id, req.params.courseId, req.body)
    }

    if (selectiveProcessStudent.courses.inputMethod.method === 'transfer') {
        reply = await replyTransferSelectiveProcesssStudent(req.params._id, req.params.courseId, req.body)
    }

    if (selectiveProcessStudent.courses.inputMethod.method === 'new_title') {
        reply = await replyNewTitleSelectiveProcesssStudent(req.params._id, req.params.courseId, req.body)
    }

    if (selectiveProcessStudent.courses.inputMethod.method === 'proof') {
        reply = await replyProofSelectiveProcesssStudent(req.params._id, req.params.courseId, req.body)
    }

    if (!reply) {
        return res.api.send('Não foi possivel salvar seus dados, entre em contato com o suporte!', res.api.codes.NOT_FOUND);
    } else if (reply.error) {
        return res.api.send(reply.error, res.api.codes.NOT_FOUND);
    }

    return res.api.send('OK', res.api.codes.OK);
};

export default get;

