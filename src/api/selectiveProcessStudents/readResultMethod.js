import {Types} from 'mongoose';
import moment from "moment";

let SelectiveProcessStudents;

const getSelectiveProcesssStudent = (selectiveProcessStudentId, courseId) => {
    const query = [
        {
            '$match':
                {
                    '_id': new Types.ObjectId(selectiveProcessStudentId)
                }
        },
        {
            '$unwind': {
                'path': '$courses'
            }
        },
        {
            '$match':
                {
                    'courses.courseId': new Types.ObjectId(courseId)
                }
        },
        {
            '$project': {
                'selectiveProcessName': '$selectiveProcess.name',
                'courseName': '$courses.name',
                'approved': '$courses.inputMethod.approved',
                'method': '$courses.inputMethod.method',
                'finished': '$courses.inputMethod.finished',
                'rated': '$courses.inputMethod.rated',
                'files': '$courses.documentsSent.files',
                'status': '$courses.status',
                'questions': '$courses.quiz.questions',
                'isPayment': '$courses.charge.isPayment',
                'class': '$courses.class',
                'enemScore': '$courses.enemScore',
                'percentageApproval': '$percentageApproval', //porcentagem para aprovação
                'hitPercentage': '$courses.quiz.hitPercentage', //porcentagem tirada na prova
                'forced': '$courses.forcedApproved', //se a aprovação foi forçada
                'dateStart': "$courses.quiz.dateStart",
                'dateEnd': "$courses.quiz.dateEnd",
                'maximumDuration': "$courses.maximumDuration",
            }
        }
    ];

    return SelectiveProcessStudents
        .aggregate(query)
        .then(result => {
            if (!result) return null;
            return result[0];
        })
        .catch(err => {
            return err;
        });
}

const get = async (req, res) => {
    SelectiveProcessStudents = req.dbs.database_piaget.models.SelectiveProcessStudents;
    let selectiveProcesssStudent = await getSelectiveProcesssStudent(req.params._id, req.params.courseId);
    const approvalPercentage = selectiveProcesssStudent.hitPercentage >= selectiveProcesssStudent.percentageApproval;

    let hitPercentage = selectiveProcesssStudent.hitPercentage;
    if (selectiveProcesssStudent.method == 'enem') {
        hitPercentage = 100 * (selectiveProcesssStudent.enemScore / 1000)
    }

    const timeSpent =  moment(selectiveProcesssStudent.dateEnd).diff(moment(selectiveProcesssStudent.dateStart), 'minute');

    const result = {
        'selectiveProcessName': selectiveProcesssStudent.selectiveProcessName,
        'courseName': selectiveProcesssStudent.courseName,
        'status': selectiveProcesssStudent.status,
        'method': selectiveProcesssStudent.method,
        'approved': selectiveProcesssStudent.approved,
        'finished': selectiveProcesssStudent.finished,
        'rated': selectiveProcesssStudent.rated,
        'isClass': !!selectiveProcesssStudent.class, //foi enturmado
        'enemScore': selectiveProcesssStudent.enemScore, //foi enturmado
        'forced': selectiveProcesssStudent.forced, //se a aprovação foi forçada
        'approvalPercentage': approvalPercentage, //foi aprovado pela porcentagem
        'hitPercentage': hitPercentage, //porcentagem tirada na prova
        'timeSpent': timeSpent < selectiveProcesssStudent.maximumDuration ? timeSpent : selectiveProcesssStudent.maximumDuration,
        'timeFinished': !(selectiveProcesssStudent.maximumDuration === -1 || timeSpent < selectiveProcesssStudent.maximumDuration),
        'isPayment': selectiveProcesssStudent.isPayment,
        'notQuestions': false,
        'notDocuments': false,
    }

    if (!selectiveProcesssStudent.questions || selectiveProcesssStudent.questions.length === 0) {
        Object.assign(result, {notQuestions: true})
    }
    if (!selectiveProcesssStudent.files || selectiveProcesssStudent.files.length === 0) {
        Object.assign(result, {notDocuments: true})
    }

    return res.api.send(result, res.api.codes.OK);
};

export default get;
