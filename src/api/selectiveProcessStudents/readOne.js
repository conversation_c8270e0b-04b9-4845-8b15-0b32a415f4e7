/**
 * Find register of SelectiveProcessStudents by id
 */
const get = (req, res) => {
    const {SelectiveProcessStudents} = req.dbs.database_piaget.models;
    return SelectiveProcessStudents
        .findById(req.params._id, req.query.project)
        .populate(req.query.populate)
        .then(data => {
            if (!data) return res.api.send(null, res.api.codes.NOT_FOUND);

            return res.api.send(data, res.api.codes.OK);
        })
        .catch(err => {
            return res.api.send(err, res.api.codes.INTERNAL_SERVER_ERROR);
        })
};

export default get;
