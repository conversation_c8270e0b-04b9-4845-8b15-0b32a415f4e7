import {Types} from 'mongoose';
import ApiRequest from "../../services/ApiRequest.service";
import moment from "moment";

let SelectiveProcessStudents;
let company;

/**
 * Find all registers of SelectiveProcessStudents collection
 */

const updateSelectiveProcesssStudent = (id, courseId, questions) => {
    return SelectiveProcessStudents
        .updateOne({"_id": new Types.ObjectId(id), "courses.courseId": new Types.ObjectId(courseId)}, {
            $set:
                {
                    'courses.$.quiz.questions': questions,
                    'courses.$.quiz.dateStart': moment()
                }
        }, {upsert: true, new: true})
        .then(result => {
            if (!result.length) return null;
            return result;
        })
        .catch(err => {
            return err;
        });
}

const getSelectiveProcesssStudent = (selectiveProcessId, courseId) => {
    const query = [
        {
            '$match':
                {
                    '_id': new Types.ObjectId(selectiveProcessId)
                }
        },
        {
            '$lookup': {
                'from': 'SelectiveProcess',
                'localField': 'selectiveProcess._id',
                'foreignField': '_id',
                'as': 'selectiveProcess'
            }
        },
        {
            '$unwind': {
                'path': '$selectiveProcess'
            }
        },
        {
            '$unwind': {
                'path': '$courses'
            }
        },
        {
            '$unwind': {
                'path': '$selectiveProcess.courses'
            }
        },
        {
            '$match':
                {
                    'courses.courseId': new Types.ObjectId(courseId)
                }
        },
        {
            '$match':
                {
                    'selectiveProcess.courses.courseId': new Types.ObjectId(courseId)
                }
        },
    ];

    return SelectiveProcessStudents
        .aggregate(query)
        .then(result => {
            if (!result) return null;
            return result[0];
        })
        .catch(err => {
            return err;
        });
}

const getGroupQuestion = (groupId, openQuestionQtd = 0, closeQuestionQtd = 0, archiveQuestionQtd = 0) => {
    let facet = {};
    if (openQuestionQtd > 0) {
        Object.assign(facet, {
            'openQuestion': [
                {
                    $match: {
                        'questions.typeOfAnswer': 'text'
                    }
                },
                {
                    $sample: {
                        'size': openQuestionQtd
                    }
                },
                {
                    $group: {
                        _id: {"$oid": groupId},
                        questions: {
                            $push: {
                                typeOfAnswer: "$questions.typeOfAnswer",
                                questionId: "$questions._id",
                                name: "$questions.name",
                                utterance: "$questions.utterance",
                                alternatives: "$questions.alternatives",
                                files: "$questions.files",
                            }
                        }
                    }
                }
            ]
        })
    }
    if (closeQuestionQtd > 0) {
        Object.assign(facet, {
            'closeQuestion': [
                {
                    '$match': {
                        'questions.typeOfAnswer': 'select'
                    }
                },
                {
                    '$sample': {
                        'size': closeQuestionQtd
                    }
                },
                {
                    $group: {
                        _id: {"$oid": groupId},
                        questions: {
                            $push: {
                                typeOfAnswer: "$questions.typeOfAnswer",
                                questionId: "$questions._id",
                                name: "$questions.name",
                                utterance: "$questions.utterance",
                                alternatives: "$questions.alternatives",
                                files: "$questions.files",
                            }
                        }
                    }
                }
            ]
        })
    }
    if (archiveQuestionQtd > 0) {
        Object.assign(facet, {
            'archiveQuestion': [
                {
                    '$match': {
                        'questions.typeOfAnswer': {'$in': ['archive', 'file']}
                    }
                },
                {
                    '$sample': {
                        'size': archiveQuestionQtd
                    }
                },
                {
                    $group: {
                        _id: {"$oid": groupId},
                        questions: {
                            $push: {
                                typeOfAnswer: "$questions.typeOfAnswer",
                                questionId: "$questions._id",
                                name: "$questions.name",
                                utterance: "$questions.utterance",
                                alternatives: "$questions.alternatives",
                                files: "$questions.files",
                            }
                        }
                    }
                }
            ]
        })
    }

    return new ApiRequest(company)
        .get('coursewares', 'group-questions', {
            aggregate: [
                {
                    $match: {
                        _id: {"$oid": groupId}
                    }
                },
                {
                    $unwind: {
                        path: '$questions'
                    }
                },
                {
                    $match: {
                        'questions.isActive': true
                    }
                },
                {
                    '$facet': facet
                }
            ]
        })
        .then(courseware => {
            return courseware;
        })
        .catch(err => {
            return err;
        });
}

const get = async (req, res) => {
    SelectiveProcessStudents = req.dbs.database_piaget.models.SelectiveProcessStudents;
    company = req.dbs.$company;
    let selectiveProcesssStudent = await getSelectiveProcesssStudent(req.params._id, req.params.courseId);

    const groupId = selectiveProcesssStudent.selectiveProcess.courses.groupQuestion._id;
    const openQuestionQtd = selectiveProcesssStudent.selectiveProcess.courses.groupQuestion.openQuestion;
    const closeQuestionQtd = selectiveProcesssStudent.selectiveProcess.courses.groupQuestion.closeQuestion;
    const archiveQuestionQtd = selectiveProcesssStudent.selectiveProcess.courses.groupQuestion.archiveQuestion;

    let questions = await getGroupQuestion(groupId, openQuestionQtd, closeQuestionQtd, archiveQuestionQtd)

    if (!questions) {
        return res.api.send('Nao existe grupo de questões!', res.api.codes.INTERNAL_SERVER_ERROR);
    } else if (questions.error) {
        return res.api.send(questions.error, res.api.codes.INTERNAL_SERVER_ERROR);
    } else {
        questions = questions.data[0]
    }

    let questionsArray = []
    if (openQuestionQtd > 0 && (!questions.openQuestion[0] || questions.openQuestion[0].questions.length !== openQuestionQtd)) {
        return res.api.send('Nao existe a quantidade de questões abertas para realizar a prova, entre em contato com a instituição!', res.api.codes.INTERNAL_SERVER_ERROR);
    } else if (openQuestionQtd > 0) {
        Array.prototype.push.apply(questionsArray, questions.openQuestion[0].questions);
    }

    if (closeQuestionQtd > 0 && (!questions.closeQuestion[0] || questions.closeQuestion[0].questions.length !== closeQuestionQtd)) {
        return res.api.send('Nao existe a quantidade de questões fechadas para realizar a prova, entre em contato com a instituição!', res.api.codes.INTERNAL_SERVER_ERROR);
    } else if (closeQuestionQtd > 0) {
        
        questions.closeQuestion[0].questions = questions.closeQuestion[0].questions.map(question => {
            question.alternatives = question.alternatives.map(alternative => {
                return {
                    alternativeId: alternative._id,
                    utteranceAlternative: alternative.utteranceAlternative,
                    correctAlternative: alternative.correctAlternative,
                }    
            })
            return question;
        })
            
            Array.prototype.push.apply(questionsArray, questions.closeQuestion[0].questions);
    }

    if (archiveQuestionQtd > 0 && (!questions.archiveQuestion[0] || questions.archiveQuestion[0].questions.length !== archiveQuestionQtd)) {
        return res.api.send('Nao existe a quantidade de questões de arquivos para realizar a prova, entre em contato com a instituição!', res.api.codes.INTERNAL_SERVER_ERROR);
    } else if (archiveQuestionQtd > 0) {
        Array.prototype.push.apply(questionsArray, questions.archiveQuestion[0].questions);
    }

    await updateSelectiveProcesssStudent(req.params._id, req.params.courseId, questionsArray)

    return res.api.send('OK', res.api.codes.OK);
};

export default get;
