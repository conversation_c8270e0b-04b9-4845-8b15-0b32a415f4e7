import {Types} from 'mongoose';
import ApiRequest from '../../services/ApiRequest.service';
import ejs from "ejs";

let SelectiveProcess;
let SelectiveProcessStudents;
let company;

//Busca o processo seletivo do estudante
const getSelectiveProcessStudent = (aggregate) => {
    return SelectiveProcessStudents.aggregate(aggregate)
};

//Busca o processo seletivo
const getSelectiveProcess = (id) => {
    return SelectiveProcess.findOne(id)
};

//renderização de templete em ejs
const createTeamplate = (text, data) => {
    text = text.replace(/&lt;/g, '<').replace(/&gt;/g, '>')
    return ejs.render(text, data);
}

//envio de email
const sendEmail = (subject, description, to, certifierNome, receiver_type = "students") => {
    const certifier = 'Certificadora ' + certifierNome;
    return new ApiRequest(company).post('new_notifications', 'notify',
        {
            title: certifier,
            "receiver_type": receiver_type,
            "receiver": "direct",
            "channel": "email",
            email: {
                to: to,
                subject: subject,
                html: description,
            },
        })
        .then(data => {
            return data
        })
        .catch(err => {
            return err
        })
}

//buscar template de modelo de comunicação de email
const getTemplate = (id) => {
    return new ApiRequest(company).get('templates', 'email-templates/' + id)
        .then(data => {
            return data
        })
        .catch(err => {
            return err
        })
}

//variaveis dinamicas
const getVariablesDynamics = (data) => {
    let result = {
        nameCertifier: data.certifier.name,
        cpfStudent: data.student.cpf,
        emailStudent: data.email,
        email: data.email,
        contract: data.enrolments && data.enrolments.contractStorageUrl ? data.enrolments.contractStorageUrl : '',
        phoneStudent: data.cellPhone,
        nameStudent: data.student.name,
        name: data.student.name,
        nameCourse: data.courses.name,
        typeCourse: data.courses.courseTypeName,
        vacancyCourse: data.courses.vacancies,
        namePolo: data.courses.polo.name,
        nameSelectiveProcess: data.selectiveProcess.name,
        typeSelectiveProcess: data.courses.inputMethod.method,
        descriptionSelectiveProcess: data.description,
        dateStartSelectiveProcess: data.dateStart,
        dateEndSelectiveProcess: data.dateEnd,
        checkoutLink: ''
    }

    if (typeof data.objLinkCheckout !== 'undefined') {
        result['checkoutLink'] = 'https://alunos.institutoprominas.com.br/checkout/' + data.objLinkCheckout._id;
    }

    return result
}

const create = async (req, res) => {
    SelectiveProcess = req.dbs.database_piaget.models.SelectiveProcess;
    SelectiveProcessStudents = req.dbs.database_piaget.models.SelectiveProcessStudents;
    company = req.dbs.$company;

    //////////////////////////////// get selective process /////////////////////////////////////////////
    let selectiveProcessStudent = await getSelectiveProcessStudent([
        {
            '$match': {
                '_id': new Types.ObjectId(req.params._id)
            }
        }, {
            '$unwind': {
                'path': '$courses'
            }
        }, {
            '$match': {
                'courses.courseId': new Types.ObjectId(req.params.courseId)
            }
        },
        {
            $lookup: {
                from: "Enrolments",
                localField: "courses.enrolmentId",
                foreignField: "_id",
                as: "enrolments"
              }
          },
          {
            $addFields: {
                enrolments: {
                  $arrayElemAt: ["$enrolments", 0]
                }
              }
          }
    ])

    if (!selectiveProcessStudent || selectiveProcessStudent.length === 0) {
        return res.api.send('Não existe o processo seletivo do estudante selecionado!', res.api.codes.NOT_FOUND);
    } else {
        selectiveProcessStudent = selectiveProcessStudent[0]
    }

    //////////////////////////////// envio de emails /////////////////////////////////////////////
    let dataVariablesEmail = getVariablesDynamics(selectiveProcessStudent)

    let selectiveProcess = await getSelectiveProcess(selectiveProcessStudent.selectiveProcess._id)

    if (!req.body.content) {
      var emailRegistration = null;
      if (typeof selectiveProcess.email_registration != 'undefined') {
        let bodyEmailRegistration = await getTemplate(selectiveProcess.email_registration)

        if (!bodyEmailRegistration) {
          return res.api.send('Não foi possivel encontrar o modelo de email de cadastro, entre em contato com o suporte!', res.api.codes.NOT_FOUND);
        } else if (bodyEmailRegistration.error) {
          return res.api.send(bodyEmailRegistration.error, res.api.codes.NOT_FOUND);
        } else {
          bodyEmailRegistration = bodyEmailRegistration.data
        }

        if (bodyEmailRegistration.communication_model === 'email') {
          let subject = bodyEmailRegistration.subject
          let description = createTeamplate(bodyEmailRegistration.description, dataVariablesEmail)
          let email = selectiveProcessStudent.email

          emailRegistration = await sendEmail(subject, description, email, selectiveProcessStudent.certifier.name)

          if (!emailRegistration) {
            return res.api.send('Não foi possivel enviar o email de registro de cadastro, entre em contato com o suporte!', res.api.codes.INTERNAL_SERVER_ERROR);
          } else if (emailRegistration.error) {
            return res.api.send(emailRegistration.error, res.api.codes.INTERNAL_SERVER_ERROR);
          }
        }
      }

      var emailEnrollment = null;
      if (typeof selectiveProcess.email_enrollment != 'undefined') {

        let bodyEmailEnrollment = await getTemplate(selectiveProcess.email_enrollment)

        if (!bodyEmailEnrollment) {
          return res.api.send('Não foi possivel encontrar o modelo de email de inscrição, entre em contato com o suporte!', res.api.codes.NOT_FOUND);
        } else if (bodyEmailEnrollment.error) {
          return res.api.send(bodyEmailEnrollment.error, res.api.codes.NOT_FOUND);
        } else {
          bodyEmailEnrollment = bodyEmailEnrollment.data
        }

        if (bodyEmailEnrollment.communication_model === 'email') {
          let subject = bodyEmailEnrollment.subject
          let description = createTeamplate(bodyEmailEnrollment.description, dataVariablesEmail)
          let email = selectiveProcessStudent.email

          emailEnrollment = await sendEmail(subject, description, email, selectiveProcessStudent.certifier.name)

          if (!emailEnrollment) {
            return res.api.send('Não foi possivel enviar o email de inscrição, entre em contato com o suporte!', res.api.codes.INTERNAL_SERVER_ERROR);
          } else if (emailEnrollment.error) {
            return res.api.send(emailEnrollment.error, res.api.codes.INTERNAL_SERVER_ERROR);
          }
        }
      }
    } else {
      emailEnrollment = await sendEmail(req.body.subject, req.body.content, selectiveProcessStudent.email, selectiveProcessStudent.certifier.name)

      if (!emailEnrollment) {
        return res.api.send('Não foi possivel enviar o email de inscrição, entre em contato com o suporte!', res.api.codes.INTERNAL_SERVER_ERROR);
      } else if (emailEnrollment.error) {
        return res.api.send(emailEnrollment.error, res.api.codes.INTERNAL_SERVER_ERROR);
      }
    }

    return res.api.send('success', res.api.codes.CREATED);
};

export default create;
