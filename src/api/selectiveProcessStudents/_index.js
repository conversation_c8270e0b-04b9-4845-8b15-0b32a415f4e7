import updateValidate from "./_validates/update.validate"

import readOne from "./readOne";
import read from "./read";
import readClassEntry from "./readClassEntry";
import update from "./update";
import readDataMethod from "./readDataMethod";

import readSelectiveProcess from "./readSelectiveProcess";
import reportSelectiveProcess from "./reportSelectiveProcess";
import readCourses from "./readCourses";
import generateQuestions from "./generateQuestions";
import readInstructionMethod from "./readInstructionMethod";
import readResultMethod from "./readResultMethod";
import resetMethod from "./resetMethod";
import replyMethod from "./replyMethod";
import autoCorrectMethod from "./autoCorrectMethod";
import autoChangeClass from "./autoChangeClass";
import resendEmailsCourse from "./resendEmailsCourse";
import approveMethod from "./approveMethod";
import checkIsFittedIn from "./checkIsFittedIn";
import cancelSelectiveProcessStudent from "./cancelSelectiveProcessStudent";

export default (route) => {
    const resource = '/selective-process-students';

    // Rota para atualizar aluno no vestibular
    route.put(`${resource}/:_id`, [
        updateValidate,
        update
    ]);

    // Rota para permitir refazer o metodo de entrada
    route.put(`${resource}/:_id/course/:courseId/method/reset`, resetMethod);////////

    // Rota para gerar o quiz
    route.put(`${resource}/:_id/course/:courseId/method/generate`, generateQuestions);////////

    // Rota para respoder o metodo
    route.put(`${resource}/:_id/course/:courseId/method/reply`, replyMethod);////////

    // Rota para corrigir o metodo
    route.put(`${resource}/:_id/course/:courseId/method/auto-correct`, autoCorrectMethod);////////

    // Rota para forçar aprovação
    route.put(`${resource}/:_id/course/:courseId/method/approve`, approveMethod);

    // Rota para forçar aprovação
    route.put(`${resource}/:_id/course/:courseId/resend-emails`, resendEmailsCourse);

    // Rota para agrupar na turma
    route.put(`${resource}/:_id/course/:courseId/auto-change-class`, autoChangeClass);

    // Rota para buscar todos os alunos do vestibular
    route.get(`${resource}`, read);////////

    // Rota para buscar todos os alunos do vestibular
    route.get(`${resource}-class-entry`, readClassEntry);////////

    // Rota para buscar todos os alunos do vestibular
    route.get(`${resource}/reportSelectiveProcess`, reportSelectiveProcess);////////

    // Rota para buscar o resultado do quiz do aluno
    route.get(`${resource}/:_id/course/:courseId/method/result`, readResultMethod);////////

    // Rota para buscar o quiz e buscar dados pertinentes para realizar o metodo de entrada do aluno
    route.get(`${resource}/:_id/course/:courseId/instruction`, readInstructionMethod);////////

    // Rota para gerar excel todos os vestibulares do aluno
    route.get(`${resource}/:cpf/selective-process`, readSelectiveProcess);////////

    // Rota para buscar todos os cursos do vestibular do aluno
    route.get(`${resource}/:_id/courses`, readCourses);////////

    // Rota para buscar um aluno do vestibular
    route.get(`${resource}/:_id`, readOne);////////

    // Rota para cancelar inscricao no vestibular (nao enturmado)
    route.post(`${resource}/:_id/course/:courseId/cancel`, cancelSelectiveProcessStudent);////////

    // Rota para checar se aluno ja foi enturmado
    route.get(`${resource}/:_id/check-fitted-in/:courseId`, checkIsFittedIn);////////

    // Rota para buscar o quiz e buscar dados pertinentes para realizar o metodo de entrada do aluno
    route.get(`${resource}/:_id/course/:courseId/method`, readDataMethod);////////

    // Rota para corrigir todos as questoes de provas
    route.get(`${resource}/courses/method/auto-correct`, autoCorrectMethod);////////

}
