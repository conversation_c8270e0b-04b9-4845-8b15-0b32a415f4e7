/**
 * Find all registers of SelectiveProcessStudents collection
 */
const list = (req, res) => {

    const {EnrolmentClassEntry} = req.dbs.database_piaget.models;
    return EnrolmentClassEntry.paginate(
        req.query.aggregate,
        req.query.limit,
        req.query.page
    ).then(
        result => {
            if (!((result || {}).data || []).length) {
                return res.api.send([], res.api.codes.OK,{paginate: 1});
            }
            return res.api.send(result.data, res.api.codes.OK, {paginate: result.paginate});
        }
    ).catch(
        err => {
            return res.api.send(err, res.api.codes.INTERNAL_SERVER_ERROR);
        }
    );
};

export default list;
