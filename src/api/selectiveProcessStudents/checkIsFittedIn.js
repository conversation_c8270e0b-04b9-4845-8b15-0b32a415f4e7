import {Types} from 'mongoose';

const isFitttedIn = async (models, id, courseId) => {
  const {SelectiveProcessStudents, Enrolments} = models;

  return SelectiveProcessStudents
    .findOne({_id: new Types.ObjectId(id)})
    .then(async selectiveProcessStudent => {
      if (!selectiveProcessStudent) return false;

      return !!(await Enrolments.findOne({
        _id: new Types.ObjectId(selectiveProcessStudent.courses.find(course => course.courseId.toString() === courseId.toString()).enrolmentId),
        'registryCourse.course.class': {$ne: null}
      }, {_id: 1}));
    });
}

const get = async (req, res) => {
  try {
    return res.api.send(await isFitttedIn(req.dbs.database_piaget.models, req.params._id, req.params.courseId), res.api.codes.OK)
  } catch (err) {
    return res.api.send(err.stack, res.api.codes.INTERNAL_SERVER_ERROR);
  }
};

export default get;
export {isFitttedIn};
