import {isFitttedIn} from "./checkIsFittedIn";
import {Types} from "mongoose";
import UserValidator from "../../services/UserValidator.service";
import moment from 'moment';

const update = async (req, res) => {
  const session = await req.dbs.database_piaget.startSession();
  await session.startTransaction();
  try {
    const {SelectiveProcessStudents, Charges, Enrolments, EnrolmentStatus, EnrolmentNotes} = req.dbs.database_piaget.models;

    const isFittedIn = await isFitttedIn(req.dbs.database_piaget.models, req.params._id, req.params.courseId);

    if (isFittedIn) return res.api.send('Alunos enturmados não podem ser cancelados', res.api.codes.NOT_ACCEPTABLE);

    const user = UserValidator.userValidate(req);
    const selectiveProcessObj = await await SelectiveProcessStudents.findOne({
      _id: new Types.ObjectId(req.params._id),
      'courses.courseId': new Types.ObjectId(req.params.courseId)
    }).lean();

    const changes = ((selectiveProcessObj || {}).changes || []).concat(((selectiveProcessObj || {}).changes || []), [{
      type: 'canceled',
      date: moment().toDate(),
      user: {
          _id: user._userId,
          name: user._userName,
      },
    }])


    const selectiveProcess = await SelectiveProcessStudents.findOneAndUpdate({
      _id: new Types.ObjectId(req.params._id),
      'courses.courseId': new Types.ObjectId(req.params.courseId)
    }, {
      $set: {
        'courses.$[courses].status': 'canceled',
        'courses.$[courses].changes': changes,
        'courses.$[courses].cancel': {
          date: new Date(),
          reason: req.body.reason,
          userName: user._userName,
          userId: user._userId,
        },
      }
    }, {new: true, session, arrayFilters: [{'courses.courseId': new Types.ObjectId(req.params.courseId)}]});

    const course = selectiveProcess.courses.find(course => course.courseId.toString() === req.params.courseId.toString());

    await Charges.updateMany({
      _enrolmentId: course.enrolmentId,
      status: 'waiting_payment'
    }, {
      $set: {
        status: 'canceled'
      }
    });

    if (course.enrolmentId) {
      await Enrolments.findOneAndUpdate(
        {
          _id: new Types.ObjectId(course.enrolmentId)
        },
        {
          $set: {
            status: 'canceled'
          }
        },
        {
          new: true,
          session
        }
      )

      await EnrolmentStatus.create(
        [{
          _enrolmentId: new Types.ObjectId(course.enrolmentId),
          _userId: user._userId,
          _userType: user._userType,
          _userName: user._userName,
          status: 'canceled',
          applyOnCombo: false,
          description: `Cancelamento via processo seletivo pelo seguinte motivo: ${req.body.reason}`,
          metadata: {
            selectiveProcessStudents: new Types.ObjectId(req.params._id)
          }
        }],
        {session}
      )

      await EnrolmentNotes.create(
        [{
          _enrolmentId: new Types.ObjectId(course.enrolmentId),
          _userId: user._userId,
          _userName: user._userName,
          applyOnCombo: false,
          description: `Cancelamento via processo seletivo pelo seguinte motivo: ${req.body.reason}`,
        }],
        {session}
      )
    }

    await session.commitTransaction();

    return res.api.send('success', res.api.codes.OK);
  } catch (err) {
    console.log(err);
    await session.abortTransaction();
    return res.api.send('Não existe processo seletivo do estudante cadastrado!', res.api.codes.INTERNAL_SERVER_ERROR);
  } finally {
    await session.endSession();
  }
};

export default update;
