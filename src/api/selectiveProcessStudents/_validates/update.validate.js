import Joi from 'joi';

export default (req, res, next) => {
    return Joi
        .object(
            {
                name: Joi.string().max(255).required(),
                email: Joi.string().max(255).required(),
                cellPhone: Joi.string().max(255).required(),
                cpf: Joi.string().max(255).required(),
                zip: Joi.string().max(255).required(),
                number: Joi.string().max(255).required(),
                city: Joi.string().max(255).required(),
                zone: Joi.string().max(255).optional(),
                birth: Joi.string().max(255).required(),
                dateStart: Joi.string().max(255).required(),
                dateEnd: Joi.string().max(255).required(),
                street: Joi.string().max(255).required(),
                complement: Joi.string().max(255).allow(null),
                state: Joi.string().max(255).required(),
                uf: Joi.string().max(255).required(),
                courses: Joi.array().items({
                        courseId: Joi.string().max(255),
                        name: Joi.string().max(255),
                        type: Joi.string().max(255),
                        polo: Joi.object({
                                _id: Joi.string().max(255).required(),
                                name: Joi.string().max(255).required(),
                            }
                        ),

                        partner: Joi.object({
                                _id: Joi.string().max(255).required(),
                            }
                        ),

                        selectiveProcessFeeAmount: Joi.number().optional(), // multiplicar por 100 quando for moeda
                        inputMethod: Joi.object({
                                method: Joi.string().max(255).valid(['enem', 'transfer', 'proof', 'new_title']).required(),
                            }
                        ),
                        enemScore: Joi.number().allow(null).optional(),
                    }
                ).allow([]),
                specialNeeds: Joi.array().items({
                        name: Joi.string().max(255).optional(),
                }).allow([])
            }
        )
        .validate(req.body, err => {
            if (err)
                return res.api.send(err.details, res.api.codes.UNPROCESSABLE_ENTITY);

            return next();
        });
}
