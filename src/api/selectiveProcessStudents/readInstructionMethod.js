import {Types} from 'mongoose';

const list = (req, res) => {
        const {SelectiveProcessStudents} = req.dbs.database_piaget.models;
        const query = [
            {
                '$match':
                    {
                        '_id': new Types.ObjectId(req.params._id)
                    }
            },
            {
                '$lookup': {
                    'from': 'SelectiveProcess',
                    'localField': 'selectiveProcess._id',
                    'foreignField': '_id',
                    'as': 's'
                }
            },
            {
                '$unwind': {
                    'path': '$s'
                }
            },
            {
                '$unwind': {
                    'path': '$s.courses'
                }
            },
            {
                '$match':
                    {
                        's.courses.courseId': new Types.ObjectId(req.params.courseId)
                    }
            },
            {
                '$project': {
                    '_id': '$_id',
                    'selectiveProcessName': '$selectiveProcess.name',
                    'selectiveProcessId': '$selectiveProcess._id',
                    'courseName': '$courses.name',
                    'courseId': '$courses.courseId',
                    'instruction': '$s.courses.descriptionForProof'
                }
            }
        ];

        return SelectiveProcessStudents
            .aggregate(query)
            .then(result => {
                return res.api.send(result[0], res.api.codes.OK);
            })
            .catch(err => {
                return res.api.send(err, res.api.codes.INTERNAL_SERVER_ERROR);
            });
    }
;

export default list;
