import {Types} from 'mongoose';
import UserValidator from '../../services/UserValidator.service';
import ApproveService from "../../services/Aprove.service";

let SelectiveProcessStudents;
let EnrolmentClassEntry;
// let company;

const getSelectiveProcesssStudent = async (_id) => {
    return await SelectiveProcessStudents.findOne({
        _id: new Types.ObjectId(_id),
    })
}

const asyncForEach = async (array, callback) => {
    for (let index = 0; index < array.length; index++) {
      await callback(array[index], index, array);
    }
};

//Atualizar processo seletivo do estudante
const updateSelectiveProcessStudent = async (id, data, courseId = null) => {
    return await SelectiveProcessStudents.findOneAndUpdate(
        {_id: new Types.ObjectId(id), "courses.courseId": new Types.ObjectId(courseId)},
        {
            $set: data.$set,
            $push: data.$push
        },
        {upsert: true}
    ).then(
        async data => data
    ).catch(
        async err => {
            throw err;
        }
    );
};

const getUser = async (req) => {
    return UserValidator.userValidate(req);
}

const saveForced = async (_id, courseId, user) => {
    let data = {
        $push: {
            "courses.$.changes":
                {
                    "type": 'forced',
                    "date": new Date(),
                    "user._id": user._userId,
                    "user.name": user._userName,
                }
        }
    }

    data['$set'] = {
        "courses.$.forcedApproved": true,
        "courses.$.status": 'approved',
    };

    return await updateSelectiveProcessStudent(_id, data, courseId);
}

const save = async (_id, courseId, user, selectiveProcessStudent, data) => {
    selectiveProcessStudent.courses.map(course => {
        if (course.courseId.toString() === courseId.toString()) {
            course.inputMethod.approved = data.approved;
            course.inputMethod.rated = true;
            course.changes.push(
                {
                    "type": data.approved ? 'approved' : 'disapproved',
                    "date": new Date(),
                    "user._id": user._userId,
                    "user.name": user._userName,
                }
            )

            course.status = data.approved ? 'approved' : 'disapproved';

            if (course.inputMethod.method === 'enem') {
                course.enemScore = data.enemScore;
                course.documentsSent.message = data.message;
                course.documentsSent.files = data.files;
            } else if (course.inputMethod.method === 'transfer' || course.inputMethod.method === 'new_title') {
                course.documentsSent.message = data.message;
                course.documentsSent.files = data.files;
            } else if (course.inputMethod.method === 'proof') {
                course.documentsSent.message = data.message;
                course.documentsSent.files = data.files;
                course.quiz.questions.map(question => {
                    const questionResponse = data.questions.find(q => {
                        return q.questionId.toString() === question.questionId.toString()
                    })

                    if (questionResponse) {
                        question.correct = questionResponse.correct;
                    }
                    return question;
                })
            }
            return course;
        }
    })

    return await selectiveProcessStudent.save();
}

const updateClassEntry = async (selectiveProcessStudentsId, courseId) => {
    return await EnrolmentClassEntry.findOneAndUpdate(
        {
            'selectiveProcess._selectiveProcessStudentsId': new Types.ObjectId(selectiveProcessStudentsId.toString()),
            'selectiveProcess._selectiveProcessCourseId': new Types.ObjectId(courseId.toString())
        },
        {
            $set: {
                approved: true
            }
        },
        {
            new: true
        }
    ).then(
        async r => r
    ).catch(
        async e => {
            console.log(e);
            return null;
        }
    );
}

const update = async (req, res) => {
    const approveService = new ApproveService(req);
    try {
        SelectiveProcessStudents = req.dbs.database_piaget.models.SelectiveProcessStudents;
        EnrolmentClassEntry = req.dbs.database_piaget.models.EnrolmentClassEntry;
       // company = req.dbs.$company;
        const id = req.params._id;
        const courseId = req.params.courseId;

        const user = await getUser(req)

        if (user.error) {
            return res.api.send(user.error.message, user.error.code);
        }

        if (!!req.body.forced && req.body.forced === true) {

            try {

                const selectiveProcessStudent = await saveForced(id, courseId, user);

                if (selectiveProcessStudent && selectiveProcessStudent.error) {
                    return res.api.send(selectiveProcessStudent.error.message, selectiveProcessStudent.error.code);
                } else if (selectiveProcessStudent && selectiveProcessStudent.length === 0) {
                    return res.api.send('Não existe processo seletivo do estudante cadastrado!', res.api.codes.NOT_FOUND);
                }
                let message = 'approved';
                await asyncForEach(selectiveProcessStudent.courses, async (course) => {
                    if (((course || {}).courseId || '').toString() === courseId.toString()) {
                        await updateClassEntry(selectiveProcessStudent._id, courseId);
                        message = await approveService.createChargesAndContracts(selectiveProcessStudent, course);
                    }
                });

                return res.api.send(message, res.api.codes.CREATED);

            } catch (e) {

                console.log(e);
                return res.api.send(e.stack, res.api.codes.INTERNAL_SERVER_ERROR);

            }

        }
        else {
            let selectiveProcessStudent = await getSelectiveProcesssStudent(req.params._id)

            const selectiveProcessStudentResult  = await save(id, courseId, user, selectiveProcessStudent, req.body)

            if (selectiveProcessStudentResult && selectiveProcessStudentResult.error) {
                return res.api.send(selectiveProcessStudentResult.error.message, selectiveProcessStudentResult.error.code);
            } else if (selectiveProcessStudentResult && selectiveProcessStudentResult.length === 0) {
                return res.api.send('Não existe processo seletivo do estudante cadastrado!', res.api.codes.NOT_FOUND);
            }

            let message = 'approved';
            if (req.body.approved) {
                await asyncForEach(selectiveProcessStudent.courses, async (course) => {
                    try {
                        if (((course || {}).courseId || '').toString() === courseId.toString()) {
                            await updateClassEntry(selectiveProcessStudent._id, courseId);
                            message = await approveService.createChargesAndContracts(selectiveProcessStudent, course);
                        }
                    } catch (err) {
                        throw new Error(err);
                    }
                })
            }

            return res.api.send(req.body.approved ? message : 'disapproved', res.api.codes.CREATED);
        }
    } catch (err) {
        console.error(err);
        return res.api.send(err.stack, res.api.codes.INTERNAL_SERVER_ERROR);
    }
};

export default update;
