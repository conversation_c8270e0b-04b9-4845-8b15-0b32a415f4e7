import {Types} from 'mongoose';
import moment from "moment";

let SelectiveProcessStudents;

/**
 * Find all registers of SelectiveProcessStudents collection
 */

const getSelectiveProcesssStudent = (selectiveProcessId, courseId) => {
    const query = [
        {
            '$match':
                {
                    '_id': new Types.ObjectId(selectiveProcessId)
                }
        },
        {
            '$unwind': {
                'path': '$courses'
            }
        },
        {
            '$match':
                {
                    'courses.courseId': new Types.ObjectId(courseId)
                }
        },
        {
            '$project':
                {
                    selectiveProcessName: '$selectiveProcess.name',
                    courseName: '$courses.name',
                    maxDuration: '$courses.maximumDuration',
                    dateStart: '$courses.quiz.dateStart',
                    questions: '$courses.quiz.questions',
                }
        },
    ];

    return SelectiveProcessStudents
        .aggregate(query)
        .then(result => {
            if (!result) return null;
            return result[0];
        })
        .catch(err => {
            return err;
        });
}

const get = async (req, res) => {
    SelectiveProcessStudents = req.dbs.database_piaget.models.SelectiveProcessStudents;
    let selectiveProcesssStudent = await getSelectiveProcesssStudent(req.params._id, req.params.courseId);

    selectiveProcesssStudent.questions = selectiveProcesssStudent.questions.map(question => {
        const alternatives = question.alternatives.map(alternative => {
            return {
                alternativeId: alternative.alternativeId,
                utteranceAlternative: alternative.utteranceAlternative,
            }
        })
        return {
            questionId: question.questionId,
            typeOfAnswer: question.typeOfAnswer,
            utterance: question.utterance,
            responseText: question.responseText,
            responseOption: question.responseOption,
            alternatives: alternatives,
        }
    })

    const dateStart = moment(selectiveProcesssStudent.dateStart)
    const dateEnd = moment()
    const maxDuration = selectiveProcesssStudent.maxDuration
    const diff = dateEnd.diff(dateStart, 'seconds')

    if(selectiveProcesssStudent.maxDuration !== -1)
      selectiveProcesssStudent.maxDuration = diff >= maxDuration * 60 ? 0 : maxDuration * 60 - diff

    delete selectiveProcesssStudent.dateStart;

    return res.api.send(selectiveProcesssStudent, res.api.codes.OK);
};

export default get;
