import {Types} from 'mongoose';

const list = (req, res) => {
        const {SelectiveProcessStudents} = req.dbs.database_piaget.models;
        const query = [
            {
                '$match': {
                    '_id': new Types.ObjectId(req.params._id)
                }
            },
            {
                '$unwind': {
                    'path': '$courses'
                }
            },
            {
                '$group': {
                    '_id': '$_id',
                    'selectiveProcessName': {
                        '$first': '$selectiveProcess.name'
                    },
                    'selectiveProcessId': {
                        '$first': '$selectiveProcess._id'
                    },
                    'courses': {
                        '$push': {
                            'name': '$courses.name',
                            'polo': '$courses.polo',
                            'status': '$courses.status',
                            'enrolmentId': '$courses.enrolmentId',
                            'documentsSent': {
                                'files': '$courses.inputMethod.finished'
                            },
                            'inputMethod': {
                                'method': '$courses.inputMethod.method',
                                'approved': '$courses.inputMethod.approved',
                                'rated': '$courses.inputMethod.rated',
                                'finished': '$courses.inputMethod.finished',
                            },
                            'charge': {
                                '_id': '$courses.charge._id',
                                'checkoutLink': '$courses.charge.checkoutLink',
                                'isPayment': '$courses.charge.isPayment'
                            },
                            'courseId': '$courses.courseId',
                        }
                    }
                }
            }
        ];

        return SelectiveProcessStudents
            .aggregate(query)
            .then(result => {
                return res.api.send(result[0], res.api.codes.OK);
            })
            .catch(err => {
                return res.api.send(err, res.api.codes.INTERNAL_SERVER_ERROR);
            });
    }
;

export default list;
