import {Types} from 'mongoose';
import jwt from 'jsonwebtoken';
import ApiRequest from "../../services/ApiRequest.service";
import {INTERNAL_SERVER_ERROR} from "http-status-codes";

let SelectiveProcessStudents;
let company;

const updateSelectiveProcessStudent = async (id, data, courseId = null) => {
    return await SelectiveProcessStudents.findOneAndUpdate(
        {_id: new Types.ObjectId(id), "courses.courseId": new Types.ObjectId(courseId)},
        data,
        {new: true}
    ).then(
        async data => {
            return data;
        }
    ).catch(
        async err => {
            if(!err.error){
                return {
                    error: {
                        message : 'Ocorreu um erro ao resetar o método de inscrição do vestibular, entre em contato com o suporte!',
                        code: INTERNAL_SERVER_ERROR
                    }
                }
            }
            return err;
        }
    );
};

const getUser = async (req, res) => {
    if (!('authorization' in req.headers)) {
        return {
            error: {
                message: 'token_not_found',
                code: res.api.codes.NOT_ACCEPTABLE
            }
        }
    }
    try {
        const payload = jwt.decode(
            req.headers.authorization.replace('Bearer ', '')
        );

        req.headers._userid = payload._userId;
        req.headers.metadata = payload.metadata;

        if (req.headers._userid) {
            const user = await new ApiRequest(company)
                .get('users', 'users/' + req.headers._userid)
                .catch(err => {
                    return err;
                });

            req.headers._userName = user.data.name;
        }

        return {
            _userid: req.headers._userid,
            _userName: req.headers._userName
        }
    } catch (err) {
        return {
            error: {
                message: err.stack,
                code: res.api.codes.INTERNAL_SERVER_ERROR
            }
        }
    }
}

const update = async (req, res) => {
    SelectiveProcessStudents = req.dbs.database_piaget.models.SelectiveProcessStudents;
    company = req.dbs.$company;
    const id = req.params._id;
    const courseId = req.params.courseId;

    const user = await getUser(req, res)

    if (user.error) {
        return res.api.send(user.error.message, user.error.code);
    }

    let data = {
        $set: {
            "courses.$.forcedApproved": false,
            "courses.$.status": 'waiting',
            "courses.$.inputMethod.approved": false,
            "courses.$.inputMethod.finished": false,
            "courses.$.inputMethod.rated": false,
            "courses.$.quiz.finishedByStudent": false,
            "courses.$.quiz.wasFixedAutomatically": false,
        },
        $unset: {
            "courses.$.documentsSent": '',
            "courses.$.enemScore": '',
            "courses.$.quiz.questions": '',
            "courses.$.quiz.dateStart": '',
            "courses.$.quiz.dateEnd": '',
            "courses.$.quiz.hitPercentage": '',
        },
        $push: {
            "courses.$.changes":
                {
                    "type": 'reset_method',
                    "date": new Date(),
                    "user._id": user._userid,
                    "user.name": user._userName,
                }
        }
    };

    let selectiveProcessStudent = await updateSelectiveProcessStudent(id, data, courseId);

    if (selectiveProcessStudent && 'error' in selectiveProcessStudent) {
        return res.api.send(selectiveProcessStudent.error.message, selectiveProcessStudent.error.code);
    } else if (!selectiveProcessStudent || !selectiveProcessStudent._id) {
        return res.api.send('Não existe processo seletivo do estudante cadastrado!', res.api.codes.NOT_FOUND);
    } else if (selectiveProcessStudent && (!('courses' in selectiveProcessStudent) || !Array.isArray(selectiveProcessStudent.courses))) {
        return res.api.send('Não existe processo seletivo do estudante cadastrado!', res.api.codes.NOT_FOUND);
    } else {
        const course = selectiveProcessStudent.courses.find(_f => _f.courseId.toString() === courseId);
        if (course && course.enrolmentId) {
            const enroll = await req.dbs.database_piaget.models.Enrolments.findOneAndUpdate(
                {_id: new Types.ObjectId(course.enrolmentId.toString())},
                {
                    $set: {
                        'registryCourse.status': 'waiting_confirm'
                    },
                    $unset: {
                        'registryCourse.dateStart': '',
                        'registryCourse.dateEnd': ''
                    }
                },
                {new: true}
            ).then(async _e => _e).catch(async () => null);
            if (!(enroll || {})._id) {
                return res.api.send('Não existe processo seletivo do estudante cadastrado!', res.api.codes.NOT_FOUND);
            }
        } else {
            return res.api.send('Não existe processo seletivo do estudante cadastrado!', res.api.codes.NOT_FOUND);
        }
    }

    return res.api.send('success', res.api.codes.CREATED);
};

export default update;
