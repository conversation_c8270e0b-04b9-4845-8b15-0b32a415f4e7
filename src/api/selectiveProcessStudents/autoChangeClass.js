import {Types} from 'mongoose';
import {INTERNAL_SERVER_ERROR} from "http-status-codes";
import ApiRequest from '../../services/ApiRequest.service';

let SelectiveProcessStudents;
let SelectiveProcess;
let Leads;
let EnrolmentClassEntry;
let company;

const thErr = (message, code = 500) => {
    const err = new Error(message);
    err.code = code;
    throw err;
};

const changeClass = async (classId, selectiveProcessStudentsId, courseId) => {
    const classEntry = await EnrolmentClassEntry.findOne(
        {
            'selectiveProcess._selectiveProcessStudentsId': new Types.ObjectId(selectiveProcessStudentsId.toString()),
            'selectiveProcess._selectiveProcessCourseId': new Types.ObjectId(courseId.toString())
        }
    ).then(
        async r => r
    ).catch(
        async e => {
            console.log(e);
            return null;
        }
    );
    if ((classEntry || {})._id) {
        return await new ApiRequest(company).put(
            'classes',
            'classes/' + classId + '/change-class/' + classEntry._id + '/' + courseId
        ).then(
            async data => {
                return data;
            }
        ).catch(
            async err => {
                console.log(err);
                return null;
            }
        );
    } else {
        return null;
    }
}

const getSelectiveProcessStudents = async (_id, courseId) => {
    return SelectiveProcessStudents
        .aggregate([
            {
                '$match': {
                    '_id': new Types.ObjectId(_id),
                },
            }, {
                '$unwind': {
                    path: '$courses',
                },
            }, {
                '$match': {
                    'courses.courseId': new Types.ObjectId(courseId),
                },
            }
        ])
        .then(data => {
            return data;
        })
        .catch(err => {
            console.log(err);
            return {
                error: {
                    message: 'Ocorreu um erro para buscar o processo seletivo do estudante, entre em contato com o suporte!',
                    code: INTERNAL_SERVER_ERROR,
                }
            }
        });
}

const getSelectiveProcess = async (_id, courseId) => {
    return SelectiveProcess
        .aggregate([
            {
                '$match': {
                    '_id': new Types.ObjectId(_id),
                },
            }, {
                '$unwind': {
                    path: '$courses',
                },
            }, {
                '$match': {
                    'courses.courseId': new Types.ObjectId(courseId),
                },
            }
        ])
        .then(data => {
            return data;
        })
        .catch(() => {
            return {
                error: {
                    message: 'Ocorreu um erro para buscar o processo seletivo, entre em contato com o suporte!',
                    code: INTERNAL_SERVER_ERROR,
                }
            }
        });
}

const getStudent = (cpf) => {
    return new ApiRequest(company)
        .get('students', 'students/cpf/' + cpf)
        .catch(err => {
            return err;
        });
}

const getLead = (email) => {
    return Leads
        .findOne({'email': email})
        .then(data => {
            return data;
        })
        .catch(() => {
            return {
                error: {
                    message: 'Ocorreu um erro para buscar o lead, entre em contato com o suporte!',
                    code: INTERNAL_SERVER_ERROR
                }
            };
        })
}

const finishLead = async (email, courseName = '') => {
    let lead = await getLead(email);
    let interestsUpdated = 0;

    if (!lead) return ;

    lead.interests = lead.interests.map(interest => {
        if (interest.course.name === courseName) {
            interest.isDone = true;
            interestsUpdated++;
        }
        return interest;
    })

    if (interestsUpdated === lead.interests.length) {
        lead.status = 'finished';
    }

    return lead.save();
}

const showMessageInServer = (message) => {
    console.log(message);
}

const get = async (req, res) => {
    SelectiveProcessStudents = req.dbs.database_piaget.models.SelectiveProcessStudents;
    SelectiveProcess = req.dbs.database_piaget.models.SelectiveProcess;
    EnrolmentClassEntry = req.dbs.database_piaget.models.EnrolmentClassEntry;
    Leads = req.dbs.database_leads.models.Leads;
    company = req.dbs.$company;
    const selectiveProcessStudentId = req.params._id;
    const courseId = req.params.courseId;

    try {
        let selectiveProcessStudents = await getSelectiveProcessStudents(selectiveProcessStudentId, courseId);

        if (selectiveProcessStudents.error) {
            thErr(selectiveProcessStudents.error.message, selectiveProcessStudents.error.code);
        } else {
            selectiveProcessStudents = selectiveProcessStudents[0];
        }

        if (selectiveProcessStudents.courses.status !== 'approved') {
            thErr('Estudante não aprovado no vestibular', res.api.codes.NOT_ACCEPTABLE);
        }

        let selectiveProcess = await getSelectiveProcess(selectiveProcessStudents.selectiveProcess._id, courseId);

        if (selectiveProcess.error) {
            thErr(selectiveProcess.error.message, selectiveProcess.error.code);
        } else {
            selectiveProcess = selectiveProcess[0];
        }

        let classes = (selectiveProcess.courses || {}).classes;

        if (!classes || Object.keys(classes).length < 1){
            return res.api.send('OK', res.api.codes.CREATED);
        }

        const random = Math.floor(Math.random() * classes.length);

        let classResult = classes[random]

        await changeClass(classResult.classId, selectiveProcessStudents._id, selectiveProcessStudents.courses.courseId);

        let student = await getStudent(selectiveProcessStudents.student.cpf);

        if (student.error) {
            thErr(student.error.message, student.error.code);
        } else {
            student = student.data;
        }

        await finishLead(student.user.email, selectiveProcessStudents.courses.name);

        return res.api.send('OK', res.api.codes.CREATED);
    } catch (e) {
        showMessageInServer(e)
        return res.api.send(e.message, e.code ? e.code : res.api.codes.INTERNAL_SERVER_ERROR);
    }
};

export default get;
