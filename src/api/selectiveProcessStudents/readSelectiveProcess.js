/**
 * Find all registers of SelectiveProcessStudents collection
 */
const list = (req, res) => {
    const {SelectiveProcessStudents} = req.dbs.database_piaget.models;
    const query = [
        {
            '$match':
                {
                    'student.cpf': req.params.cpf
                }
        },
        {
            '$lookup': {
                'from': 'SelectiveProcess',
                'localField': 'selectiveProcess._id',
                'foreignField': '_id',
                'as': 's'
            }
        }, {
            '$unwind': {
                'path': '$s'
            }
        }, {
            '$project': {
                'cpf': '$cpf',
                'email': '$email',
                'selectiveProcessName': '$selectiveProcess.name',
                'selectiveProcessDescription': '$s.description',
                'validity': '$dateEnd'
            }
        }
    ];
    
    return SelectiveProcessStudents
        .paginate(query, req.query.limit, req.query.page)
        .then(result => {
            if (!result.data.length) return res.api.send(null, res.api.codes.NOT_FOUND);
            return res.api.send(result.data, res.api.codes.OK, {paginate: result.paginate});
        })
        .catch(err => {
            return res.api.send(err, res.api.codes.INTERNAL_SERVER_ERROR);
        });
};

export default list;
