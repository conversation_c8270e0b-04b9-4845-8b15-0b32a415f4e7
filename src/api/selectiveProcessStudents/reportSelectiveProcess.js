import SpreadsheetService from '../../services/SPREADSHEET/Spreadsheet.service';
/**
 * Find all registers of SelectiveProcessStudents collection
 */

const _generateXlsx = async (data) => {
    const spreadsheetService = new SpreadsheetService();
    const arrMethods = {
        'enem': "Enem",
        'transfer': "Transferência",
        'proof': "Avaliação",
        'new_title': "Obtenção de novo título"
    }

    const columns = [
        {name: '<PERSON><PERSON>'},
        {name: 'Email'},
        {name: 'Telefone'},
        {name: '<PERSON>urs<PERSON>'},
        {name: 'Metódo de entrada '},
        {name: 'CPF'},
        {name: 'Taxa de Inscrição'},
        {name: 'Data de Inscrição'},
        {name: 'Status da Matrícula'},
        {name: 'Taxa de Matrícula Paga'},
        {name: 'Mensalidade Paga'}
    ];

    const formatedData = await data.map(item => [
        item.name,
        item.email,
        item.cellPhone,
        item.courseName,
        arrMethods[item.inputMethod],
        item.cpf,
        item.havePayment ? (item.financial == true ? 'Pago' : 'Aberto') : "",
        item.created,
        enrollStatus(item.enrolment),
        enrollFee(item.charges),
        monthlyPaid(item.charges)
    ]);

    spreadsheetService.createWorksheet(
        'Vestibular',
        columns,
        formatedData
    );

    return spreadsheetService.generateTableBuffer();
};

const enrollStatus = (enrolment) => {
    const arrEnrollStatus = {
        'matriculate': 'Matriculado',
        'waiting_confirm': 'Aguardando confirmação',
        'completed': 'Completo',
        'awaiting_approval_by_deferral': 'Aguardando aprovação por deferimento',
        'rejected_by_deferral': 'Indeferida'
    }

    if (enrolment) {
        if (enrolment.registryCourse) {
            return arrEnrollStatus[enrolment.registryCourse.status] ? arrEnrollStatus[enrolment.registryCourse.status] : enrolment.registryCourse.status;
        }
        return '-';
    }
    return '-';
}

const enrollFee = (charges) => {
    if (charges.find(charge => charge._chargeTypeAlias === 'rate-enrolment')) {
        let charge = charges.find(charge => charge._chargeTypeAlias === 'rate-enrolment');
        return 'R$ ' + charge.amount/100;
    } else if (charges.find(charge => charge._chargeTypeAlias === 'rate-enrolment-monthly')) {
        let charge = charges.find(charge => charge._chargeTypeAlias === 'rate-enrolment-monthly');
        if (((charge.metadata || {}).apportionment || {})['rate-enrolment']) {
            return 'R$ ' + charge.metadata.apportionment['rate-enrolment']/100;
        } else {
            return '-';
        }
    } else {
        return '-'
    }
};

const monthlyPaid = (charges) => {
    if (charges.find(charge => charge._chargeTypeAlias === 'monthly' && charge.installment === 1)) {
        let charge = charges.find(charge => charge._chargeTypeAlias === 'monthly' && charge.installment === 1);
        return 'R$ ' + charge.amount/100;
    } else if (charges.find(charge => charge._chargeTypeAlias === 'rate-enrolment-monthly' && charge.installment === 1)) {
        let charge = charges.find(charge => charge._chargeTypeAlias === 'rate-enrolment-monthly' && charge.installment === 1);
        if (((charge.metadata || {}).apportionment || {})['monthly']) {
            return 'R$ ' + charge.metadata.apportionment['monthly']/100;
        } else {
            return '-';
        }
    } else {
        return '-'
    }
}

const report = (req, res) => {

    const {SelectiveProcessStudents} = req.dbs.database_piaget.models;

    return SelectiveProcessStudents
        .aggregate(req.query.aggregate)
        .then(async result => {
            if (!result.length) {
                return res.api.send(null, res.api.codes.NOT_FOUND);
            }

            let buffer =  await _generateXlsx(result);
            
            res.setHeader(
                'Content-Type',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            );
            res.setHeader(
                'Content-Disposition',
                'attachment; filename=teste.xlsx'
            );
            return res.send(buffer);
        })
        .catch(err => {
            console.error(err);

            return res.api.send(err, res.api.codes.INTERNAL_SERVER_ERROR);
        });
};

export default report;
