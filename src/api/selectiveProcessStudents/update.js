import {Types} from 'mongoose';
import ApiRequest from "../../services/ApiRequest.service";

let SelectiveProcessStudents;
let company;

//Atualizar estudante
const updateStudent = (id, data) => {
    return new ApiRequest(company)
        .put('students', 'students/' + id, data)
        .then(data => {
            return data;
        })
        .catch(err => {
            return err;
        });
};

//Atualizar processo seletivo do estudante
const updateSelectiveProcessStudent = (id, data, idCourse = null) => {
    return SelectiveProcessStudents
        .findOneAndUpdate(
            {_id: new Types.ObjectId(id), "courses.courseId": new Types.ObjectId(idCourse)},
            {$set: data},
        )
        .then(data => {
            return data;
        })
        .catch(err => {
            return err
        });
};

const update = async (req, res) => {
    SelectiveProcessStudents = req.dbs.database_piaget.models.SelectiveProcessStudents;
    company = req.dbs.$company;
    const data = req.body;

    let courseId = data.courses[0].courseId;
    let dataSelectiveProcessStudent = {
        "courses.$.polo.name": data.courses[0].polo.name,
        "courses.$.polo._id": data.courses[0].polo._id,
        "courses.$.inputMethod.method": data.courses[0].inputMethod.method,
        "courses.$.enemScore": data.courses[0].enemScore,
        'dateStart': data.dateStart,
        'dateEnd': data.dateEnd,
        'email': data.email,
        'cellPhone': data.cellPhone,
        'student.name': data.name,
        'student.cpf': data.cpf,
        'student.specialNeeds': data.specialNeeds,
    }

    let selectiveProcessStudent = await updateSelectiveProcessStudent(req.params._id, dataSelectiveProcessStudent, courseId);

    if (selectiveProcessStudent.error) {
        return res.api.send(selectiveProcessStudent.error.message, selectiveProcessStudent.error.code);
    } else if (selectiveProcessStudent.length === 0) {
        return res.api.send('Não existe processo seletivo do estudante cadastrado!', res.api.codes.NOT_FOUND);
    }

    let idStudent = selectiveProcessStudent.student._id;

    let student = {
        'specialNeeds': data.specialNeeds,
        'birthDate': data.birth,
        'cellPhone': data.cellPhone,
        'address': {
            'city': data.city,
            'complement': data.complement,
            'number': data.number,
            'uf': data.uf,
            'street': data.street,
            'zip': data.zip,
            'zone': data.zone,
        },
        'cpf': data.cpf,
        'email': data.email,
        'name': data.name,
    }

    const studentUpdate = await updateStudent(idStudent, student);

    if (studentUpdate.error) {
        return res.api.send(studentUpdate.error.message, studentUpdate.error.code);
    } else if (studentUpdate.length === 0) {
        return res.api.send('Não existe o estudante cadastrado!', res.api.codes.NOT_FOUND);
    }

    return res.api.send('success', res.api.codes.CREATED);
};

export default update;
