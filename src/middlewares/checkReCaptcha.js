import request   from 'request-promise';
import requestIp from 'request-ip';
import ApiConfig from '../config/api.conf';

const studentPortalLoginWhiList = [
    // '************'
];
// '<EMAIL>', '<EMAIL>'


export default (action, minScore = 0.7) => {
    return async (req, res, next) => {
        const env = new ApiConfig().getEnv();
        const proxyIp = '_userip' in req.headers ? req.headers._userip : undefined;
        const token = req.body.reCaptcha;
        const useRecaptchaV2 = req.body.useRecaptchaV2;
        const {ReCaptchaBlocks} = req.dbs.database_piaget.models;

        delete req.body.reCaptcha;
        delete req.body.useRecaptchaV2;

       // if (process.env.NODE_ENV === 'development') return next();

        const secretKey = env.recaptcha.secretKey;
        const secretKeyv2 = env.recaptcha.secretKeyv2;
        const optionalSecretKey = env.recaptcha.optionalSecretKey;
        const ip = proxyIp || requestIp.getClientIp(req);

        if (action === 'studentPortalLogin' && (studentPortalLoginWhiList.includes(req.body.email) || studentPortalLoginWhiList.includes(proxyIp)))
            return next();

        let uri = `https://www.google.com/recaptcha/api/siteverify?secret=${secretKey}&response=${token}&remoteip=${ip}`;
        console.log(' linhe 33  ', uri)
        let result = await request({
            uri,
            method: 'POST',
            json  : true
        });

        console.log("var line40 = ",JSON.stringify(result))

        if (optionalSecretKey && !(result || {}).success) {
            uri = `https://www.google.com/recaptcha/api/siteverify?secret=${optionalSecretKey}&response=${token}&remoteip=${ip}`;
            console.log(' optionalSecretKey ', uri)
            result = await request({
                uri,
                method: 'POST',
                json  : true
            });

            console.log("var line51 =",JSON.stringify(result))
        }

        if (useRecaptchaV2) {
            uri = `https://www.google.com/recaptcha/api/siteverify?secret=${secretKeyv2}&response=${token}&remoteip=${ip}`;
            console.log(' recaptchaV2 ', uri)
            result = await request({
                uri,
                method: 'POST',
                json  : true
            });
            console.log("var line60 = ",JSON.stringify(result))
        }

        if ((result || {}).success) {
            if (action !== result.action && !useRecaptchaV2) {
                await ReCaptchaBlocks.create({
                    uri     : req.uri,
                    headers : req.headers,
                    params  : req.params,
                    query   : req.query,
                    body    : req.body,
                    action  : {
                        requested: action,
                        token    : result.action
                    },
                    score   : {
                        min   : minScore,
                        actual: result.score
                    },
                    ip,
                    response: 'Sistema anti-fraude: requisicão inválida'
                });

                return res.api.send('Sistema anti-fraude: requisicão inválida (captcha)', res.api.codes.BAD_REQUEST);
            }

            if (result.score < minScore && !useRecaptchaV2) {
                await ReCaptchaBlocks.create({
                    uri     : req.uri,
                    headers : req.headers,
                    params  : req.params,
                    query   : req.query,
                    body    : req.body,
                    action  : {
                        requested: action,
                        token    : result.action
                    },
                    score   : {
                        min   : minScore,
                        actual: result.score
                    },
                    ip,
                    response: 'Sistema anti-fraude: acesso negado'
                });

                return res.api.send('Sistema anti-fraude: acesso negado (captcha)', res.api.codes.BAD_REQUEST);
            }

            return next();
        }

        await ReCaptchaBlocks.create({
            uri     : req.uri,
            headers : req.headers,
            params  : req.params,
            query   : req.query,
            body    : req.body,
            action  : {
                requested: action,
                token    : result.action || '-'
            },
            score   : {
                min   : minScore,
                actual: result.score || '-'
            },
            result,
            ip,
            response: 'Re-Captcha inválido, atualize a página'
        });

        return res.api.send('Re-Captcha inválido, atualize a página (captcha)', res.api.codes.BAD_REQUEST);
    };
};
