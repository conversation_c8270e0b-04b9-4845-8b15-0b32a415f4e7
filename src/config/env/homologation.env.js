 
module.exports = {
  "prominas": {
    "app": {
      "name": "MICROSERVICE VESTIBULAR - HOMO MODE",
      "version": "1.0.0",
      "locale": "pt_BR",
      "timezone": "America/Sao_Paulo",
      "adminEmail": "<EMAIL>",
      "sendEmailErrors": false,
      "disableCron": true,
      "recapcha": "6LfMPUsaAAAAAGuIkG8Uv0cjWlhAR-otgly_2wBT"
    },
    "recaptcha": {
      "secretKey": "6LdGtdoaAAAAAFvgbt0XCpwwMNA7fE9VZQ5hgYq_",
      "secretKeyv2": "6LeH0YApAAAAAO95F8-7kNYa_-9rEChMLBrZZs7N"
    },
    "activeCampaign": {
      "enabled": true,
      "apiUrl": "https://grupoprominas68189.api-us1.com",
      "apiLink": "api/3",
      "token": "539b72053efc216405453e04f2a2ea1199dee952b7318ea49cdac734a638ad831d8d4a1a"
    },
    "mail": {
      "host": "smtplw.com.br",
      "port": 465,
      "secure": true,
      "auth": {
        "user": "Prominassigesp",
        "pass": "zJfFMHdg8398"
      },
      "sender": "<EMAIL>"
    },
    "server": {
      "secure": false,
      "host": "0.0.0.0",
      "port": 3320,
      "cors": {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
        'Access-Control-Allow-Headers': 'Origin, X-Requested-With, Content-Type, Accept, Authorization, company, referer, sec-ch-ua, sec-ch-ua-mobile, sec-ch-ua-platform, user-agent'
      },
      ssl: {
        privateKey: '',
        certificate: '',
        hpkpKeys: []
      }
    },
    databases: {
      prominas: {
        database_leads: {
          'servers': [
            {
              'host': 'erp-lyratec-homologatio-shard-00-00.cm4wf.mongodb.net:27017',
              'port': 27017
            },
            {
              'host': 'erp-lyratec-homologatio-shard-00-01.cm4wf.mongodb.net:27017',
              'port': 27017
            },
            {
              'host': 'erp-lyratec-homologatio-shard-00-02.cm4wf.mongodb.net:27017',
              'port': 27017
            }
          ],
          "replicaSet": "atlas-gl7ro8-shard-0",
          "serversSrv": "erp-lyratec-homologatio.cm4wf.mongodb.net",
          "appName": "erp-lyratec-homologatio",
          'authSource': 'admin',
          'auto_reconnect': true,
          'ssl': true,
          'user': 'homologation',
          'pass': '88D1AvR0bYV0G61Q',
          'database': 'database_leads',
          'dialect': 'mongodb',
          'charset': 'utf8',
          'logging': false,
          'enabled': true,
          'configWith': 'mongoose'
        },
        database_piaget: {
          'servers': [
            {
              'host': 'erp-lyratec-homologatio-shard-00-00.cm4wf.mongodb.net:27017',
              'port': 27017
            },
            {
              'host': 'erp-lyratec-homologatio-shard-00-01.cm4wf.mongodb.net:27017',
              'port': 27017
            },
            {
              'host': 'erp-lyratec-homologatio-shard-00-02.cm4wf.mongodb.net:27017',
              'port': 27017
            }
          ],
          "replicaSet": "atlas-gl7ro8-shard-0",
          "serversSrv": "erp-lyratec-homologatio.cm4wf.mongodb.net",
          "appName": "erp-lyratec-homologatio",
          'authSource': 'admin',
          'auto_reconnect': true,
          'ssl': true,
          'user': 'homologation',
          'pass': '88D1AvR0bYV0G61Q',
          'database': 'database_piaget',
          'dialect': 'mongodb',
          'charset': 'utf8',
          'logging': false,
          'enabled': true,
          'configWith': 'mongoose'
        }
      }
    },
    rdStation: {
      code: 'b221059bbaafbde3a224af805cad294a',
      client_id: 'b4e50730-0e3b-47a6-8708-2249d1a12278',
      client_secret: '36d18f6ed0c945b88fd7b761a7d1c5e4',
      refresh_token: 'VIaVhM9-B2ea1sybITJNIJ6iMgisx4UjUyicbEwSrNg'
    },
    gateway: 'https://homo-api-gateway.institutoprominas.com.br/',
    apis: {
      payments: {
        mode: 'direct',
        baseUrl: 'http://localhost:3087/'
      },
      "partners": {
        "mode": "direct",
        "baseUrl": "http://localhost:3004/"
      },
      "finances": {
        "mode": "direct",
        "baseUrl": "http://localhost:3009/"
      },
      "courses": {
        "mode": "direct",
        "baseUrl": "http://localhost:3005/"
      },
      "coursewares": {
        "mode": "direct",
        "baseUrl": "http://localhost:3007/"
      },
      "api_mailer": {
        "mode": "direct",
        "baseUrl": "http://localhost:6000/"
      },
      "websites": {
        "mode": "direct",
        "baseUrl": "http://localhost:7000/"
      },
      "priceplan": {
        "mode": "direct",
        "baseUrl": "http://localhost:3003/"
      },
      "piagetPublicApi": {
        "mode": "direct",
        "baseUrl": "http://localhost:5555/"
      },
      "notifications": {
        "mode": "direct",
        "baseUrl": "http://localhost:3010/"
      },
      "rdStation": {
        "mode": "direct",
        "baseUrl": "http://localhost:5678/"
      },
      "students": {
        "mode": "direct",
        "baseUrl": "http://localhost:3014/"
      },
      "templates": {
        "mode": "direct",
        "baseUrl": "http://localhost:3016/"
      },
      "new_notifications": {
        "mode": "direct",
        "baseUrl": "http://localhost:3988/"
      },
      "classes": {
        "mode": "direct",
        "baseUrl": "http://localhost:6505/"
      }
    }
  }
};
