
/* eslint-env node */
module.exports = {
  prominas: {
    app: {
      name: 'MICROSERVICE VESTIBULAR - PROD MODE',
      version: '1.0.0',
      locale: 'pt_BR',
      timezone: 'America/Sao_Paulo',
      adminEmail: '<EMAIL>',
      sendEmailErrors: true,
      disableCron: false,
      recapcha: '6LdGtdoaAAAAAFvgbt0XCpwwMNA7fE9VZQ5hgYq_'
    },
    recaptcha: {
      secretKey: '6LffpDcqAAAAAHVae41v7ukC0PJ8o3fiQhaBfP7X',
      secretKeyv2: '6LempDcqAAAAALVLdYYVvoJAPOUAHqzF-Q49u5GN',
      optionalSecretKey: '6LdGtdoaAAAAAFvgbt0XCpwwMNA7fE9VZQ5hgYq_'
    },
    activeCampaign: {
      enabled: true,
      apiUrl: 'https://grupoprominas68189.api-us1.com',
      apiLink: 'api/3',
      token: '539b72053efc216405453e04f2a2ea1199dee952b7318ea49cdac734a638ad831d8d4a1a'
    },
    mail: {
      host: 'smtplw.com.br',
      port: 465,
      secure: true,
      auth: {
        user: 'Prominassigesp',
        pass: 'zJfFMHdg8398'
      },
      sender: '<EMAIL>'
    },

    server: {
      secure: false,
      host: '0.0.0.0',
      port: 3320,
      cors: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Origin, X-Requested-With, Content-Type, Accept, Authorization, company, referer, sec-ch-ua, sec-ch-ua-mobile, sec-ch-ua-platform, user-agent'
      },
      ssl: {
        privateKey: '',
        certificate: '',
        hpkpKeys: []
      }
    },

    databases: {
      prominas: {
        database_piaget: {
          servers: [
            {
              host: 'erp-lyratec-shard-00-00.dyz6u.mongodb.net',
              port: 27017
            },
            {
              host: 'erp-lyratec-shard-00-01.dyz6u.mongodb.net',
              port: 27017
            },
            {
              host: 'erp-lyratec-shard-00-02.dyz6u.mongodb.net',
              port: 27017
            }
          ],
          replicaSet: 'atlas-t55wyk-shard-0',
          "serversSrv": "erp-lyratec.dyz6u.mongodb.net",
          "appName": "erp-lyratec",
          authSource: 'admin',
          auto_reconnect: true,
          ssl: true,
          user: 'piaget',
          pass: 'LCGOK4lHXFcFD3io',
          database: 'database_piaget',
          dialect: 'mongodb',
          charset: 'utf8',
          logging: false,
          enabled: true,
          configWith: 'mongoose'
        },
        database_leads: {
          servers: [
            {
              host: 'leads-shard-00-00.dyz6u.gcp.mongodb.net',
              port: 27017
            },
            {
              host: 'leads-shard-00-01.dyz6u.gcp.mongodb.net',
              port: 27017
            },
            {
              host: 'leads-shard-00-02.dyz6u.gcp.mongodb.net',
              port: 27017
            }
          ],
          replicaSet: 'atlas-rppghe-shard-0',
          "serversSrv": "leads-pri.dyz6u.gcp.mongodb.net",
          "appName": "leads-shard",
          authSource: 'admin',
          auto_reconnect: true,
          ssl: true,
          user: 'leads',
          pass: 'wbgmArChbkkmU6Nr',
          database: 'leads',
          dialect: 'mongodb',
          charset: 'utf8',
          logging: false,
          enabled: true,
          configWith: 'mongoose',
        }
      }
    },

    gateway: 'http://api-gateway.institutoprominas.com.br/',

    apis: {
      users: {
        mode: 'direct',
        baseUrl: 'http://**********:3000/'
      },
      payments: {
        mode: 'direct',
        baseUrl: 'http://**********:3087/'
      },
      api_mailer: {
        mode: 'direct',
        baseUrl: 'http://**********:6000/'
      },
      courses: {
        mode: 'direct',
        baseUrl: 'http://**********:3005/'
      },
      priceplan: {
        mode: 'direct',
        baseUrl: 'http://**********:3003/'
      },
      finances: {
        mode: 'direct',
        baseUrl: 'http://**********:3009/'
      },
      templates: {
        mode: 'direct',
        baseUrl: 'http://**********:3016/'
      },
      websites: {
        mode: 'direct',
        baseUrl: 'http://**********:7000/'
      },
      piagetPublicApi: {
        mode: 'direct',
        baseUrl: 'https://api-lyratec.institutoprominas.com.br/'
      },
      chat: {
        mode: 'direct',
        baseUrl: 'https://sala-estudos.institutoprominas.com.br:5533/'
      },
      notifications: {
        mode: 'direct',
        baseUrl: 'http://**********:3010/'
      },
      rdStation: {
        mode: 'direct',
        baseUrl: 'https://rd.institutoprominas.com.br/'
      },
      sonax: {
        mode: 'direct',
        baseUrl: 'http://api.sonax.net.br/'
      },
      students: {
        mode: 'direct',
        baseUrl: 'http://10.138.0.21:3014/'
      },
      new_notifications: {
        mode: 'direct',
        baseUrl: 'http://**********:3988/'
      }
    },

    rdStation: {
      code: 'b221059bbaafbde3a224af805cad294a',
      client_id: 'b4e50730-0e3b-47a6-8708-2249d1a12278',
      client_secret: '36d18f6ed0c945b88fd7b761a7d1c5e4',
      refresh_token: 'VIaVhM9-B2ea1sybITJNIJ6iMgisx4UjUyicbEwSrNg'
    },

    links: {
      recoverPassword: 'http://parceiros.institutoprominas.com.br'
    }
  }
};
