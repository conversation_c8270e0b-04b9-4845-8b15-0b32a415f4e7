import fs   from 'fs';
import path from 'path';

export default {
    bcrypt: {
        saltRounds: 10
    },
    jwt   : {
        key       : fs.readFileSync(path.join(__dirname, '../../storage/certificates/token.key'), 'utf8'),
        cert      : fs.readFileSync(path.join(__dirname, '../../storage/certificates/token.crt'), 'utf8'),
        expiresIn : '1w',
        algorithms: [
            'RS256',
            'RS384',
            'RS512'
        ]
    }
}
