import superTest from 'supertest';
import assert    from 'assert';
import app       from '../../app';

const server = superTest.agent(app);

// Wait for the app to start completely
before( (done) => {
    app.on("apiStarted", () => {
        done();
    });
});

describe("Example tests", () => {

    describe('Welcome', () => {

        it('should be create initial data where request /', (done) => {
            server
                .get('/')
                .end((err, res) => {
                    assert.deepEqual(res.body.code, 201);
                    assert.deepEqual(res.body.message, 'success_on_create');
                    done();
                });
        });

        it('should be response with success on request /', (done) => {
            server
                .get('/')
                .end((err, res) => {
                    assert.deepEqual(res.body.code, 200);
                    assert.deepEqual(res.body.message, 'success');
                    done();
                });
        });

        it('should be response with error on request /no_exists', (done) => {
            server
                .get('/no_exists')
                .end((err, res) => {
                    assert.deepEqual(res.body.code, 404);
                    done();
                });
        });

    });
});