import dotenv from 'dotenv';
import child_process from 'child_process';
import path from 'path';
import fs from 'fs';
 
import express from 'express';
import httpContext from 'express-http-context';
import bodyParser from 'body-parser';
import compression from 'compression';
import morgan from 'morgan';
import mongoose from 'mongoose';
import bluebird from 'bluebird';

global.Promise = bluebird.Promise;

// Config
import ApiConfig from './config/api.conf';

// Define environment object
const config = new ApiConfig();
const environment = config.getEnv();
const isTestingDeploy = process.argv.includes('--test-deploy');

const getRandPort = (min, max) => {
  return Math.floor(Math.random() * (max - min + 1)) + min;
};

if (isTestingDeploy) environment.server.port = getRandPort(6000, 6999);

const envFilePath = path.resolve(__dirname, '..', '.env');

if (fs.existsSync(envFilePath))
  require('dotenv').config({ path: envFilePath });


// Core
import Cors from './core/Cors';
import Routers from './core/Routers';
import Database from './core/Database';
import RequestQuery from './core/RequestQuery';
import SSL from './core/SSL';
import Security from './core/Security';
import Response from './core/Response';
import Locales from './core/Locales';
import Validator from './core/Validator';
import LogsManager from './core/LogsManager';



// Classes & app
const app = express();
const cors = new Cors();
const routers = new Routers();
const database = new Database();
const requestQuery = new RequestQuery();
const ssl = new SSL();
const security = new Security();
const response = new Response();
const locales = new Locales(environment.app.locale);
const validator = new Validator();
const logsManager = new LogsManager(environment);

require("@babel/register");
require("@babel/polyfill");

process.env.TZ = environment.app.timezone;


process.prependListener('uncaughtException', error => {
  console.log(error);if (config.getEnvName() === 'development' || isTestingDeploy) throw error;
  else logsManager.log(error.stack);
});

/*process.on('unhandledRejection', (reason, promise) => {
  if (config.getEnvName() === 'development' || isTestingDeploy) throw new Error(reason);
  else logsManager.log(new Error(reason));
});*/


// Set express app in Response class
response.setApp(app);

/**
 * Setup validator with Joi
 * @private
 */
const _setupValidator = () => {
    // Set locale in validator
    validator.setLocale(locales.locale, locales.getLocaleObject('joi'));
    validator.syncSettings();
};

/**
 * Use routes in app
 * @private
 */
const _setupRouters = () => {
    routers.syncRouters(app);
};

/**
 * Console log output
 * @param text
 * @private
 */
const _appLog = (text) => {
    if (config.getEnvName() !== 'test') {
        console.log(text)
    }
};

/**
 * Set the HTTP headers for cors and others
 * @private
 */
const _setupCors = () => {
    environment.server.cors['x-powered-by'] = environment.app.name;
    cors.setCors(app, environment.server.cors)
};

/**
 * Set databases properties and connect
 * @private
 */
const _setupDatabase = async () => {

    // Define cors headers
    _setupCors();

    // Define validator configs
    _setupValidator();

    await database.connectDatabases(environment, locales);

    _appLog('[Databases]\tConnect success!!');

    _setupRouters();

    //await routines._setupRoutines();
};


/**
 * After Express listen with success run the setups functions...
 * @private
 */
const _listenSuccess = () => {

    // Init databases
    _setupDatabase();

    // Print in console app status
    _appLog(`\n${environment.app.name} on at ${environment.server.host}:${environment.server.port}\n`);
    if (isTestingDeploy) process.exit(0);


    // Detect if app is running in secure mode and print this
    if (environment.server.secure) {
        _appLog('[SSL_ON]\tSecure')
    } else {
        _appLog('[SSL_OFF]\tNOT SECURE (!)')
    }
};

// No use logs in test environment!
if (config.getEnvName() !== 'test') {
    app.use(morgan(config.getEnvName() === 'development' ? 'dev' : 'combined'));
}
const companyChecker = (req, res, next) => {

    const exceptions = [
        '/health'
    ];
    if (exceptions.includes(req.originalUrl.split("?").shift())) {
        return next();
    }

    const company = (req.headers.company && process.env.NODE_ENV !== 'production') ? req.headers.company : "prominas";

    if (process.env.NODE_ENV !== 'production') console.log("req.headers : ",JSON.stringify(req.headers)," - company : ",company)

    if (!company) return res.status(400).send({message: 'Company header is not set, please tell us what company you want to use.'});
    if (!(company in mongoose.companies)) return res.status(400).send({message: `Company "${company}" is not configured, please change the company or implement this company.`});

    req.dbs = mongoose.companies[company];
    req.dbs.$company = company;

    httpContext.set('dbs', mongoose.companies[company]);
    httpContext.set('company', company);

    return next();
};

// Express global usages and middlewares
app.use(bodyParser.json());
app.use(requestQuery.parseQuery);
app.use(compression({threshold: 100}));
app.use(companyChecker);

// Security middlewares with helmet
security.makeSecure(app, environment.server.ssl.hpkpKeys);

// Create secure server or insecure server (see your *.env.js)
const server = environment.server.secure ? ssl.getHTTPSServer(app, environment.server.ssl) : app;

// Listen server
server.listen(environment.server.port, environment.server.host, _listenSuccess);







const loadSecretEnv = async () => {
  // Constrói o caminho absoluto para o script bash
  const scriptPath = path.join(__dirname, '..', 'bash_scripts', 'load_secret_env.sh');
  const nodeEnv = process.env.NODE_ENV || 'development'; // Garante que NODE_ENV tenha um valor

  return new Promise((resolve, reject) => {
    console.log('[SECRETS] Carregando env...');

    child_process.exec(`bash "${scriptPath}" ${nodeEnv} --env`, (error, stdout, stderr) => {
      if (error) {
        console.error(`Erro ao executar o script: ${error.message}`);
        return reject(error);
      }

      if (stderr) {
        console.error(`Aviso (stderr): ${stderr}`);
        // Considere se você realmente quer rejeitar a promise em caso de stderr.
      }

      return Promise
        .map(stdout.split('\n'), envVar => {
          const vars = dotenv.parse(envVar);

          Object.keys(vars).forEach(key => {
            process.env[key] = vars[key].replaceAll('\\', '');
          })
        }, {concurrency: 10})
        .then(envVars => resolve(envVars))
        .catch(error => reject(error));
    });
  });
};

export default app;
