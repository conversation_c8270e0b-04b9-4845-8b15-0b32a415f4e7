import jwt from 'jsonwebtoken';
import authConfig from '../config/auth/auth.conf';

export default class {
  constructor() {
  }

  valid(token) {
    try {
      jwt.verify(token, authConfig.jwt.cert, {
        algorithms: authConfig.jwt.algorithms
      });

      return true;
    } catch (e) {
      return false;
    }
  }

  createToken(payload, expiresIn) {
    return jwt.sign(payload, authConfig.jwt.key, {
      algorithm: authConfig.jwt.algorithms[Math.floor(Math.random() * authConfig.jwt.algorithms.length)],
      expiresIn: expiresIn
    });
  }

  createBearerToken(payload, expiresIn = authConfig.jwt.expiresIn) {
    return 'Bearer ' + this.createToken(payload, expiresIn);
  }

  decode(token) {
    return jwt.decode(token);
  }
}
