
import fs from 'fs';
import path from 'path';
import ejs from 'ejs';
import ApiRequestService from '../ApiRequest.service';

const EMAIL_TEMPLATES = {
    'Timóteo'       : {
        BOAS_VINDAS     : 'unica-vale-do-aco/email-01-boas-vindas',
        REDACAO         : 'unica-vale-do-aco/email-02-redacao',
        ENEM            : 'unica-vale-do-aco/email-04-enem',
        RESULTADO       : 'unica-vale-do-aco/email-05-resultado',
    },
    'Contagem'      : {
        BOAS_VINDAS     : 'unica-contagem/email-01-boas-vindas',
        REDACAO         : 'unica-contagem/email-02-redacao',
        ENEM            : 'unica-contagem/email-04-enem',
        RESULTADO       : 'unica-contagem/email-05-resultado',
    },
    'Ipatinga'      : {
        BOAS_VINDAS     : 'unica-vale-do-aco/email-01-boas-vindas',
        REDACAO         : 'unica-vale-do-aco/email-02-redacao',
        ENEM            : 'unica-vale-do-aco/email-04-enem',
        RESULTADO       : 'unica-vale-do-aco/email-05-resultado',
    },
    'Montes Claros' : {
        BOAS_VINDAS     : 'prominas-montes-claros/email-01-boas-vindas',
        REDACAO         : 'prominas-montes-claros/email-02-redacao',
        ENEM            : 'prominas-montes-claros/email-04-enem',
        RESULTADO       : 'prominas-montes-claros/email-05-resultado',
    },
    default         : {
        BOAS_VINDAS     : '',
        REDACAO         : '',
        ENEM            : '',
        RESULTADO       : '',
    },
};

class ExamService{
  constructor(req) {
      this.req = req;
  }

  // Check if grade is ok
    checkGrade(body) {
        if(body.dissertation && body.dissertation.grade) {
            return {approved: body.approved, 'dissertation.grade': body.dissertation.grade};
        }
        return body;
    }

    getEmailTemplates(local) {
        if (local.type === 'Polo') {
            return EMAIL_TEMPLATES.default;
        }

        return EMAIL_TEMPLATES[local.name] || EMAIL_TEMPLATES.default;
    }

    sendEmail(student, email) {

        if (this.req.dbs.$company !== 'prominas') return;

        fs.readFile(path.join(__dirname, `../../storage/email/${email.template}.ejs`), async (err, templateContent) => {
            try {
                const content = ejs.render(templateContent.toString(), Object.assign({
                        name: student.name,
                        link: email.link
                    })
                );

                await new ApiRequestService(this.req.dbs.$company).post('new_notifications', 'notify', {
                    'title': 'Vestibular Online - Grupo Prominas',
                    'receiver': 'direct',
                    'receiver_type': 'all',
                    'channel': 'email',
                    'email': {
                        to: student.email,
                        subject: email.subject,
                        html: content
                    }
                });
            } catch(err) {
                console.error(err);
            }
        });
    }
}

export default ExamService;
