


import moment from "moment/moment";
import ApiRequestService from "./ApiRequest.service";
import ChargeServices from "./ChargeServices.service";
import {Types} from "mongoose";
import ejs from "ejs";
import { NOT_FOUND } from "http-status-codes";

export default class ApproveService {

    constructor(req) {
        this.req = req;
    }

    async createContract(_enrolmentId) {
        const response = await new ApiRequestService(this.req.dbs.$company).post('students', `contracts/create/${_enrolmentId}`);

        return JSON.parse(response).data;
    }

    async createCharges(enrolment) {
        let paymentMonth      = this.getPaymentMonth();
        let paymentDay        = await this.getPaymentDay();


        const firstMonthlyDueDate = ((enrolment.metadata || {firstMonthlyDueDate: null}).firstMonthlyDueDate || null);

        if (firstMonthlyDueDate) {
            paymentMonth = moment(firstMonthlyDueDate).month() + 1;
            paymentDay   = parseInt(moment(firstMonthlyDueDate).format('DD'));
        }

        enrolment.paymentDay    = paymentDay;
        enrolment.paymentMonth  = paymentMonth;

        const chargeService = new ChargeServices(enrolment, null, this.req);
        const data = chargeService.setChargeData(enrolment._id, enrolment.enrolment.notUsedRateEnrolment);

        return chargeService.saveCharge(data);
    };

    async getPaymentDay() {
        const config = await this.req.dbs.database_piaget.models.Configurations.findOne({name: 'options_of_payment_day', isActive: true});

        if (!config || !config.value || !config.value.monthly.length) return Number(moment().format('DD'));

        return  config.value.monthly[config.value.monthly.length - 1].value;
    }

    getPaymentMonth() {
        return parseInt(moment().add(1, 'month').month(), 10) + 1;
    }

    getVariablesDynamics(data) {
        return {
            cpfStudent: data.student.cpf,
            emailStudent: data.user.email,
            email: data.user.email,
            phoneStudent: data.student.cellPhone || data.student.phone || data.student.whatsApp,
            contract: '',
            nameStudent: data.student.name,
            name: data.student.name,
            nameCertifier: data.objSelectiveProcessStudent.certifier.name,
            nameCourse: data.objSelectiveProcessStudent.courses[0].name,
            typeCourse: data.objSelectiveProcessStudent.courses[0].courseTypeName,
            vacancyCourse: data.objSelectiveProcessStudent.courses[0].vacancies,
            namePolo: data.objSelectiveProcessStudent.courses[0].polo.name,
            nameSelectiveProcess: data.objSelectiveProcessStudent.name,
            typeSelectiveProcess: data.objSelectiveProcessStudent.courses[0].inputMethod.method,
            descriptionSelectiveProcess: data.objSelectiveProcessStudent.description,
            dateStartSelectiveProcess: data.objSelectiveProcessStudent.dateStart,
            dateStartSelectiveProcessBr: moment(data.objSelectiveProcessStudent.dateStart).format("DD/MM/YYYY"),
            dateEndSelectiveProcess: data.objSelectiveProcessStudent.dateEnd,
            dateEndSelectiveProcessBr: moment(data.objSelectiveProcessStudent.dateEnd).format("DD/MM/YYYY"),
            checkoutLink: '',
            boleto: ''
        };
    }

    async getTemplate (id) {
        return await new ApiRequestService(this.req.dbs.$company).get('templates', 'email-templates/' + id).then(
            async template => {
                if (!template.data) {
                    return {
                        error: {
                            message: 'Não foi possivel encontrar um modelo de email, entre em contato com o suporte!',
                            code: NOT_FOUND,
                        }
                    }
                }
                return template.data
            }
        ).catch(
            async () => {
                return {
                    error: {
                        message: 'Ocorreu um erro para buscar um template de email do estudante, entre em contato com o suporte!',
                        code: 'INTERNAL_SERVER_ERROR',
                    }
                }
            }
        );
    }

    createTeamplate(text, data) {
        text = text.replace(/&lt;/g, '<').replace(/&gt;/g, '>')
        return ejs.render(text, data);
    }

    async sendEmail (subject, description, to, certifierNome, receiver_type = "students") {
        // const certifier = 'Certificadora ' + certifierNome;
        return await new ApiRequestService(this.req.dbs.$company).post(
            'new_notifications',
            'notify',
            {
                title: subject,
                "receiver_type": receiver_type,
                "receiver": "direct",
                "channel": "email",
                email: {
                    to: to,
                    subject: subject,
                    html: description,
                },
            }
        ).then(
            async data => {
                if (!data) {
                    return {
                        error: {
                            message: 'Não foi possivel enviar o email  do estudante, entre em contato com o suporte!',
                            code: 'INTERNAL_SERVER_ERROR',
                        }
                    };
                }
                return data;
            }
        ).catch(
            async err => {
                console.log(err);
                return {
                    error: {
                        message: 'Ocorreu um erro para enviar o email do estudante, entre em contato com o suporte!',
                        code: 'INTERNAL_SERVER_ERROR',
                    }
                };
            }
        );
    }

    async sendMessage(selectiveProcess, selectiveProcessStudent, course, enrolment, student, user, indication) {
        const bitrixConfig = await this.req.dbs.database_piaget.models.Configurations.findOne({name: 'bitrix_config', isActive: true});
        let dataVariablesEmail = this.getVariablesDynamics({
            student: student,
            user: user,
            objSelectiveProcessStudent: selectiveProcessStudent
        });
        // let inputMethod = '';
        // switch ((((course || {}).inputMethod || {}).method || '').toString()) {
        //     case 'enem':
        //         inputMethod = 'ENEM';
        //     break;
        //     case 'proof':
        //         inputMethod = 'Avaliação';
        //     break;
        //     case 'transfer':
        //         inputMethod = 'Transferência';
        //     break;
        //     case 'new_title':
        //         inputMethod = 'Obtenção de Novo Título';
        //     break;
        //     default:
        //         inputMethod = 'Não Informado'
        //         break;
        // }
        if (((bitrixConfig || {}).value || {}).useWelcome) {
           await new ApiRequestService(this.req.dbs.$company).post(
                'new_notifications',
                'notify',
                {
                    title: dataVariablesEmail.nameCertifier,
                    receiver_type: "students",
                    receiver: "direct",
                    channel: "bitrix",
                    bitrix: {
                        cpf: enrolment.cpf,
                        certifier: enrolment.registryCourse.course._certifierName,
                        course: enrolment.registryCourse.course._name,
                        email: user.email,
                        phone: student.cellPhone || student.phone || student.whatsApp || '',
                        enrolmentId: enrolment.contractStorageURL || enrolment._id,
                        name: (indication || {}).name || '',
                        partner: (indication || {}).name || '',
                        origin: 'Lyraedu Vestibular Aprovação',
                        studentName: student.name,
                        createdAt: moment(enrolment.createdAt ? enrolment.createdAt : undefined).format("DD/MM/YYYY HH:mm:ss"),
                        type: 'welcome'
                    }
                }
            );
        } else {
            let bodyEmailEnrollment = await this.getTemplate(selectiveProcess.email_enrollment);

            if (!bodyEmailEnrollment.error) {
                if (bodyEmailEnrollment.communication_model === 'email') {
                    let subject = this.createTeamplate(bodyEmailEnrollment.subject || bodyEmailEnrollment.name, dataVariablesEmail);
                    let description = this.createTeamplate(bodyEmailEnrollment.description, dataVariablesEmail);
                    await this.sendEmail(subject, description, dataVariablesEmail.emailStudent, selectiveProcess.certifier.name);
                }
            }
        }
    }

    async createChargesAndContracts(selectiveProcessStudent, course) {
      const enrolment = await this.req.dbs.database_piaget.models.Enrolments.findOne({
          _id: new Types.ObjectId(course.enrolmentId)
      });
      const charges = await this.req.dbs.database_piaget.models.Charges.find({
          _enrolmentId: new Types.ObjectId(course.enrolmentId),
          _chargeTypeAlias: 'monthly',
          status: {$in: ['waiting_payment', 'paid',  'free']}
      });
      const student = await this.req.dbs.database_piaget.models.Students.findOne({
          cpf: enrolment.cpf
      });
      if(!student) {
          throw new Error('Estudante não encontrado');
      }
      const user = await this.req.dbs.database_piaget.models.Users.findOne({
          _id: new Types.ObjectId(((student || {})._userId || new Types.ObjectId()))
      });
      if(!user) {
          throw new Error('Usurário não encontrado');
      }
      const selectiveProcess = await this.req.dbs.database_piaget.models.SelectiveProcess.findOne({
          _id: new Types.ObjectId(selectiveProcessStudent.selectiveProcess._id.toString())
      });
      if(!selectiveProcess) {
          throw new Error('Processo seletivo não encontrado');
      }
      const acl = await this.getAcl('student');
      if(!acl) {
          throw new Error('Acl de estudante não encontrada');
      }
      const isAdmin = user?.isAdmin ?? false;
      const aclIds = user?._aclId ?? [];
      const hasStudentAcl = aclIds.some(_aclId => _aclId.toString() === acl._id.toString());

      if (!isAdmin && !hasStudentAcl) {
        // Adiciona acl._id se ainda não estiver presente
        aclIds.push(acl._id);
        await this.req.dbs.database_piaget.models.Users.updateOne({_id: user._id}, {$set: {_aclId: aclIds}});
      }

      let indicationName;
      let indication = {
          cpf: '',
          name: ''
      };
      if (enrolment && !!enrolment.indication && Array.isArray(enrolment.indication) && enrolment.indication.length > 0) {
          const indicationObj = enrolment.indication.find(_i => (_i.level || '').toString().toLowerCase() === 'master');
          if (
              indicationObj &&
              typeof indicationObj === 'object' &&
              indicationObj.cpf &&
              ['partner', 'employer', 'student'].includes((indicationObj.userType || '').toString().toLowerCase())
          ) {
              indication.cpf = indicationObj.cpf;
              switch ((indicationObj.userType || '').toString().toLowerCase()) {
                  case 'partner':
                      indicationName = await this.req.dbs.database_piaget.models.Partners.findOne({
                          'documents.cpf': indication.cpf
                      });
                      break;
                  case 'employer':
                      indicationName = await this.req.dbs.database_piaget.models.Employers.findOne({
                          cpf: indication.cpf
                      });
                      break;
                  case 'student':
                      indicationName = await this.req.dbs.database_piaget.models.Students.findOne({
                          cpf: indication.cpf
                      });
                      break;
              }
              if (indicationName && indicationName.name) {
                  indication.name = indicationName.name;
              }
          }
      }

      // Possui link do storage do template e link do storage do contrato
      const contractObjects =  await this.createContract(course.enrolmentId)
      enrolment.templateContract   = contractObjects.templateContract;
      enrolment.contractStorageURL = contractObjects.contractStorageURL;

      if (!(charges || []).length) await this.createCharges(enrolment);
      await this.sendMessage(selectiveProcess, selectiveProcessStudent, course, enrolment, student, user, indication);

      // Como foi isento ou não trabalha com taxa de matricula, já deve ficar na situação de matriculado
      // if(enrolment.enrolment.notUsedRateEnrolment || !enrolment.enrolment.amount){
      //     enrolment.registryCourse.status = 'matriculate';
      // }

      await enrolment.save();

      return charges.length > 0 ? 'charges_not_created' : 'approved';
    };

    checkPaymentDayIsValid(paymentMonth, paymentDay) {
        if (moment(paymentMonth, 'MM').daysInMonth() < paymentDay) return  Number(moment(paymentMonth, 'MM').endOf('month').format('DD'))

        return paymentDay
    }

    async getAcl() {
        try {
            const response = await this.req.dbs.database_piaget.models.Acls.findOne({alias: 'student'});

            return response;
        } catch (err) {
            throw Error(err);
        }
    }
}
