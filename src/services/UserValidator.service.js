/* eslint-disable quotes,no-undefined,max-statements */

class UserValidator {

    static userValidate(req) {

        let _userId = '5ee763ccf3f6c129adb414c3';
        let _userType = 'employer';
        let _userName = 'Computer';

        if ('_userid' in req.headers) {
            _userId = req.headers._userid;
        }

        if ('_usertype' in req.headers) {
            _userType = req.headers._usertype;
        }

        if ('_username' in req.headers) {
            _userName = req.headers._username;
        }

        return {
            _userId: _userId,
            _userType: _userType,
            _userName: _userName
        };
    }
}
export default UserValidator;