/* eslint-disable camelcase,no-console,newline-per-chained-call */
const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));
import moment from 'moment';
import ApiConfig from "../config/api.conf";

export default class {

    constructor() {

        const env = new ApiConfig().getEnv();

        this.enabled = ((env || {}).activeCampaign || {}).enabled || false;
        this.apiUrl = ((env || {}).activeCampaign || {}).apiUrl || '';
        this.apiLink = ((env || {}).activeCampaign || {}).apiLink || '';
        this.token = ((env || {}).activeCampaign || {}).token || '';
        this.headers = {
            Accept: 'application/json',
            'Content-Type': 'application/json',
            'api-token': this.token
        };
    }

    async sendData(data) {

        if (!this.enabled) {
            return;
        }

        const contactInfo = await fetch(`${this.apiUrl}/${this.apiLink}/contacts?email=${data.student.email}`, {
            method: 'GET',
            headers: this.headers
        }).then(response => response.json()).catch(err => err);

        let id = null;
        let urlId = '';
        if (!!contactInfo && !!contactInfo.contacts && Array.isArray(contactInfo.contacts) && contactInfo.contacts.length > 0) {
            id = contactInfo.contacts[0].id;
            urlId = `/${id}`;
        }

        let inputMethod = null;
        switch(data.type) {
            case 'enem':
                inputMethod = 'ENEM';
                break;
            case 'dissertation':
                inputMethod = 'Redação';
                break;
            case 'transfer':
                inputMethod = 'Tranferência';
                break;
            case 'new_title':
                inputMethod = 'Obtenção de Novo Titulo';
                break;
            case 'return':
                inputMethod = 'Retorno';
                break;
        }

        let stepName = 'Selecionar Curso';
        let stepNumber = 1;
        switch(data.step) {
            case 'enem-redaction':
                stepName = 'Selecionar a Forma de Ingresso';
                stepNumber = 2;
                break;
            case 'redaction-view':
                stepName = 'Selecionar o Tema da Redação';
                stepNumber = 3;
                break;
            case 'redaction-write':
                stepName = 'Entrega da Redação';
                stepNumber = 4;
                break;
            case 'enem-note':
                stepName = 'Informar Nota do Enem';
                stepNumber = 4;
                break;
            case 'other-forms':
                stepName = 'Selecionar a Forma de Ingresso';
                stepNumber = 4;
                break;
            case 'address':
                stepName = 'Finalizar Formulário';
                stepNumber = 5;
                break;
        }

        const contactObj = {
            firstName: data.student.name,
            email: data.student.email,
            phone: data.student.phone,
            fieldValues: [
                {
                    field: '7', //MODALIDADE
                    value: data.course.type
                },
                {
                    field: '37', //UNIDADE/POLO
                    value: data.local.name
                },
                {
                    field: '24', //CURSO
                    value: data.course.name
                },
                {
                    field: '38', //Tipo de Ingresso
                    value: stepNumber > 1 ? inputMethod : null
                },
                {
                    field: '39', //Status do Formulário
                    value: stepName
                },
                {
                    field: '40', //Ano da Prova do ENEM
                    value: stepNumber > 3 && inputMethod === 'ENEM' ? data.enem.year : null
                },
                {
                    field: '41', //Média Total do ENEM
                    value: stepNumber > 3 && inputMethod === 'ENEM' ? data.enem.grade : null
                },
                {
                    field: '42', //Tema da Redação
                    value: stepNumber > 2 && inputMethod === 'Redação' ? data.dissertation.theme : null
                },
                {
                    field: '43', //Link da Redação
                    value: stepNumber > 3 && inputMethod === 'Redação' ? data.dissertation.link : null
                },
                {
                    field: '44', //CPF
                    value: stepNumber > 4 ? data.student.cpf : null
                },
                {
                    field: '45', //Data de Nascimento
                    value: stepNumber > 4 && data.student.birthDate ? ( /^\d{2}\/\d{2}\/\d{4}$/g.test(data.student.birthDate) ? data.student.birthDate : moment(data.student.birthDate.toString()).format('DD/MM/YYYY') ) : null
                },
                {
                    field: '46', //Rua
                    value: stepNumber > 4 && !!data.student.address ? data.student.address.street : null
                },
                {
                    field: '47', //Número
                    value: stepNumber > 4 && !!data.student.address ? data.student.address.number : null
                },
                {
                    field: '48', //Complemento
                    value: stepNumber > 4 && !!data.student.address ? data.student.address.complement : null
                },
                {
                    field: '49', //Cep
                    value: stepNumber > 4 && !!data.student.address ? data.student.address.zip : null
                },
                {
                    field: '50', //Cidade
                    value: stepNumber > 4 && !!data.student.address ? data.student.address.city : null
                },
                {
                    field: '51', //Estado
                    value: stepNumber > 4 && !!data.student.address ? data.student.address.uf : null
                }

            ]
        };

        const options = {
            method: !!id ? 'PUT' : 'POST',
            headers: this.headers,
            body: JSON.stringify({
                contact: contactObj
            })
        };
        const contact = await fetch(`${this.apiUrl}/${this.apiLink}/contacts${urlId}`, options).then(response => response.json()).catch(err => console.error(err));

        if (!!contact && !!contact.contact && !!contact.contact.id) {

            const list = await fetch(`${this.apiUrl}/${this.apiLink}/contactLists`, {
                method: 'POST',
                headers: this.headers,
                body: JSON.stringify({
                    contactList: {
                        list: (!!data.listId && !isNaN(data.listId)) ? data.listId : 21,
                        contact: contact.contact.id,
                        status: 1
                    }
                })
            }).then(response => response.json()).catch(err => console.error(err));

        }

    }
}
