


import moment from "moment/moment";
import ApiRequestService from "./ApiRequest.service";

class ChargeServices {

    constructor(data, voucher, req) {
        this.dbs = req.dbs;
        this.data = data;
        this.voucher = voucher;
    }

    setChargeData(_enrolmentId, notUsedRateEnrolment = false) {

        const dataArray = [];

        let free = false;

        if (notUsedRateEnrolment || !this.data.enrolment.amount) {

            dataArray.push({
                _enrolmentId: _enrolmentId,
                _chargeTypeAlias: 'rate-enrolment',
                installment: 1,
                amount: 0,
                dueDate: moment(),
                status: 'free'
            });

            const onEnrolmentPaymentPlan = ((this.data.metadata || {}).onEnrolmentPaymentPlan || {});

            let valueCourse = Object.keys((onEnrolmentPaymentPlan || {})).length ? (onEnrolmentPaymentPlan.value * onEnrolmentPaymentPlan.installment) : this.data.registryCourse.courseAmount.amount;

            if ('scholarshipPercent' in this.data.registryCourse.courseAmount) {
                valueCourse -= (valueCourse * this.data.registryCourse.courseAmount.scholarshipPercent / 100);
            }

            if ('voucher' in this.data.registryCourse.courseAmount && !('scholarshipPercent' in this.data.registryCourse.courseAmount)) {
                switch (this.data.registryCourse.courseAmount.voucher.amountType) {
                    case 'percentage':
                        valueCourse -= (valueCourse * this.data.registryCourse.courseAmount.voucher.amount / 100);
                        break;
                    case 'value':
                        if (this.data.registryCourse.courseAmount.voucher.amount >= this.data.registryCourse.courseAmount.amount) {
                            valueCourse = 0;
                        } else {
                            valueCourse -= this.data.registryCourse.courseAmount.voucher.amount;
                        }
                        break;
                }
                // Updated Voucher if usage
                this.updateVoucher();

                if (valueCourse < 0) valueCourse = 0
            }

            if ('interest' in this.data.registryCourse.courseAmount) {
                valueCourse += (valueCourse * this.data.registryCourse.courseAmount.interest / 100);
            }

            let parcels = ((((this.data.metadata || {}).onEnrolmentPaymentPlan) || {installment: null}).installment) || this.data.registryCourse.courseAmount.installment;

            if (
                 this.data.metadata &&
                 this.data.metadata.prices &&
                 this.data.metadata.prices.course &&
                 this.data.metadata.prices.course.paymentMethod &&
                 this.data.metadata.prices.course.paymentMethod === 'creditCard'
               ) {
                parcels = 1;
            }

            let valueInstallment = parseInt(valueCourse) / parseInt(parcels);
            let dayOfPayment = this.data.paymentDay;
            for (let installment = 1; installment <= parcels; installment++) {

                /* Calcula data de vencimento, calcula quantos meses o aluno escolheu de prazo, baseado na data da inscrição e no mês escolhido ao inscrever */
                const enrolDay = moment();
                const addYears = this.data.paymentMonth - 1 < enrolDay.get('months') ? 1 : 0;

                const dueDate = moment()
                    .add(this.data.paymentMonth - moment().format('MM') + installment - 1, 'month')
                    .startOf('month')
                    .add(addYears, 'years')
                    .startOf('day');

                dayOfPayment = this.checkPaymentDayIsValid(dueDate, this.data.paymentDay)

                dueDate.add((dayOfPayment-1),'days')

                dataArray.push({
                    _enrolmentId: _enrolmentId,
                    _chargeTypeAlias: 'monthly',
                    installment: installment,
                    amount: valueInstallment,
                    dueDate: dueDate,
                    status: valueInstallment > 0 ? 'waiting_payment' : 'free'
                });
            }
            return dataArray;
        }

        /**
         * Verificar se a taxa de inscrição está isenta e gerar as cobranças de inscrição e mensalidade
         */
        if ('isPaidToPartner' in this.data.enrolment) {

            if (this.data.enrolment.isPaidToPartner) {

                dataArray.push({
                    _enrolmentId: _enrolmentId,
                    _chargeTypeAlias: 'rate-enrolment',
                    installment: 1,
                    amount: 0,
                    dueDate: moment(),
                    paymentDate: moment(),
                    status: 'free'
                });

                free = true;
            }

        }

        if (!free) {

            /**
             * Caso não seja isenta pega o valor da taca de inscrição
             * @type {fields.enrolment.amount|{type, index, required}|*}
             */

            let valueEnrolment = this.data.enrolment.amount;

            /**
             * Verificar se é bolsista na taxa de inscrição
             */
            if ('scholarshipPercent' in this.data.enrolment) {
                valueEnrolment -= (valueEnrolment * this.data.enrolment.scholarshipPercent / 100);
            }

            /**
             * Verificar voucher para aplicar sobre o valor da taxa de inscrição
             */
            if ('voucher' in this.data.enrolment && !('scholarshipPercent' in this.data.enrolment)) {
                switch (this.data.enrolment.voucher.amountType) {
                    case 'percentage':
                        valueEnrolment -= (valueEnrolment * this.data.enrolment.voucher.amount / 100);
                        break;
                    case 'value':
                        valueEnrolment -= this.data.enrolment.voucher.amount;
                        break;
                }

                // Updated Voucher if usage
                this.updateVoucher();
            }

            /**
             * Se o valor da inscrição estiver 0
             * coloca o valor da inscrição como isento
             */
            if (!valueEnrolment) {
                dataArray.push({
                    _enrolmentId: _enrolmentId,
                    _chargeTypeAlias: 'rate-enrolment',
                    installment: 1,
                    amount: 0,
                    dueDate: moment(),
                    status: 'free'
                });

                free = true;

            } else if (this.data.enrolment.paymentMethod === 'creditCard') {
                if ('interest' in this.data.enrolment) {
                    valueEnrolment += (valueEnrolment * this.data.enrolment.interest / 100);
                }

                dataArray.push({
                    _enrolmentId: _enrolmentId,
                    _chargeTypeAlias: 'rate-enrolment',
                    installment: 1,
                    amount: valueEnrolment,
                    dueDate: moment(this.data.enrolment.dueDate)
                });
            } else {

                /**
                 * Calcular os juros e parcelar
                 * @type {number}
                 */

                if ('interest' in  this.data.enrolment) {
                    valueEnrolment += (valueEnrolment * this.data.enrolment.interest / 100);
                }

                const valueInstallment = parseInt(valueEnrolment) / parseInt(this.data.enrolment.installment);

                /**
                 * Caso a parcela for maior que 1 gera as parcelas da taxa de inscrição
                 */
                if (this.data.enrolment.installment > 1) {

                    for (let i = 0; this.data.enrolment.installment > i; i++) {

                        let dueDate = null;

                        if (i == 0) {
                            dueDate = moment(this.data.enrolment.dueDate);
                        }

                        if (i > 0) {
                            dueDate = moment(this.data.enrolment.dueDate).add(i, 'months');
                        }

                        dataArray.push({
                            _enrolmentId: _enrolmentId,
                            _chargeTypeAlias: 'rate-enrolment',
                            installment: i + 1,
                            amount: valueInstallment,
                            dueDate: dueDate,
                        });
                    }
                } else {

                    /**
                     * Caso o valor seja a vista
                     */
                    dataArray.push({
                        _enrolmentId: _enrolmentId,
                        _chargeTypeAlias: 'rate-enrolment',
                        installment: 1,
                        amount: valueEnrolment,
                        dueDate: moment(this.data.enrolment.dueDate)
                    });
                }

            }
        }


        if (free) {

            /**
             * Gerar plano de pagamento do curso
             */

            let valueCourse = this.data.registryCourse.courseAmount.amount;
            // const installmentValue = this.data.registryCourse.courseAmount.installment; not used

            if ('scholarshipPercent' in this.data.registryCourse.courseAmount) {
                valueCourse -= (valueCourse * this.data.registryCourse.courseAmount.scholarshipPercent / 100);
            }

            if ('voucher' in this.data.registryCourse.courseAmount && !('scholarshipPercent' in this.data.registryCourse.courseAmount)) {
                switch (this.data.registryCourse.courseAmount.voucher.amountType) {
                    case 'percentage':
                        valueCourse -= (valueCourse * this.data.registryCourse.courseAmount.voucher.amount / 100);
                        break;
                    case 'value':
                        valueCourse -= this.data.registryCourse.courseAmount.voucher.amount;
                        break;
                }
                // Updated Voucher if usage
                this.updateVoucher();
            }

            if ('interest' in this.data.registryCourse.courseAmount) {
                valueCourse += (valueCourse * this.data.registryCourse.courseAmount.interest / 100);
            }

            let valueInstallment = parseInt(valueCourse) / parseInt(this.data.registryCourse.courseAmount.installment);

            if (this.data.registryCourse.courseAmount.paymentMethod === 'creditCard') {

                dataArray.push({
                    _enrolmentId: _enrolmentId,
                    _chargeTypeAlias: 'monthly',
                    installment: 1,
                    amount: valueCourse,
                    dueDate: moment(),
                    paymentDate: moment(),
                });

            }

            if (!valueCourse || valueCourse === 0) {
                dataArray.push({
                    _enrolmentId: _enrolmentId,
                    _chargeTypeAlias: 'monthly',
                    installment: 1,
                    amount: 0,
                    dueDate: moment(),
                    paymentDate: moment(),
                    status: 'free'
                });

                return dataArray;
            }

            if (this.data.registryCourse.courseAmount.paymentMethod === 'boleto' && this.data.registryCourse.courseAmount.installment > 1 ) {

                for (let installment = 1; installment <= this.data.registryCourse.courseAmount.installment; installment++) {

                    /* Calcula data de vencimento, calcula quantos meses o aluno escolheu de prazo, baseado na data da inscrição e no mês escolhido ao inscrever */
                    const enrolDay = moment();
                    const addYears = this.data.paymentMonth - 1 < enrolDay.get('months') ? 1 : 0;

                    const month = this.data.paymentMonth + (installment - 1);

                    const paymentDay = this.checkPaymentDayIsValid(month, this.data.paymentDay, this.data.newPaymentDay);

                    const dueDate = moment()
                        .add(month - parseInt(moment().format('MM')), 'month')
                        .startOf('month')
                        .add(paymentDay - 1, 'days')
                        .add(addYears, 'years')
                        .startOf('day');

                    dataArray.push({
                        _enrolmentId: _enrolmentId,
                        _chargeTypeAlias: 'monthly',
                        installment: installment,
                        amount: valueInstallment,
                        dueDate: dueDate
                    });
                }
            }
        }

        return dataArray;

    }

    /**
     * POST cadastrar várias cobranças e salvar histórico
     * Lógica criada na Rota
     * @param data
     */
    async saveCharge(data) {
        const apiRequest = new ApiRequestService(this.dbs.$company);
        return await apiRequest
            .post('finances', 'charges', data)
            .then(async res => {
                return res.data;
            })
            .catch(async err => {
                return err;
                // if (err.error.code == '404') return false;
            })
    }

    /**
     * PUT atualizar o uso do Voucher
     * Lógica criada na Rota
     */
    updateVoucher() {
        if (this.voucher) {
            if (this.voucher.validateType === 'usage') {
                const apiRequest = new ApiRequestService(this.dbs.$company);
                return apiRequest
                    .put('priceplan', 'vouchers_usage/' + this.voucher._id);
            }
        }
    }

    checkPaymentDayIsValid(paymentMonth, paymentDay) {
        if (moment(paymentMonth, 'MM').daysInMonth() < paymentDay) return  Number(moment(paymentMonth, 'MM').endOf('month').format('DD'))

        return paymentDay
    }
}

export default ChargeServices;
