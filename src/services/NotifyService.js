 
import {Types} from 'mongoose';

class NotifyService {
    constructor(req, courseware) {
        this.req = req;
        this.courseware = courseware;
    }

    send() {
        return this
            ._findEnrolments()
            .then(enrolments => this.req.dbs.database_piaget.Messages.create(enrolments.map(_e => {
                return {
                    _userId       : _e._userId,
                    _userType     : 'student',
                    _certifierName: _e.certifier,
                    title         : 'Material Didático Atualizado',
                    message       : `O Material Didático "${this.courseware.title}" acabou de ser atualizado, confira as novidades acessando o AVA.`,
                    sender        : {
                        _userId         : new Types.ObjectId('000000000000000000000000'),
                        _userName       : 'Computer',
                        _departamentName: 'Material Didático'
                    }
                };
            })));
    }

    _findEnrolments() {
        return this.req.dbs.database_piaget.Enrolments.aggregate([
            {
                $match: {
                    'registryCourse.course.disciplines._coursewares': new Types.ObjectId(this.courseware._id)
                }
            },
            {
                $lookup: {
                    from        : 'Students',
                    localField  : 'cpf',
                    foreignField: 'cpf',
                    as          : 'student'
                }
            },
            {
                $unwind: '$student'
            },
            {
                $project: {
                    _id      : 0,
                    certifier: '$registryCourse.course._certifierName',
                    _userId  : '$student._userId'
                }
            }
        ]);
    }
}

export default NotifyService;
