const Excel = require('exceljs/dist/es5');

class SpreadsheetService {
    constructor() {
        this.workbook = new Excel.Workbook();
    }

    generateTableBuffer() {
        return new Promise((resolve) => {
            resolve(this.workbook.xlsx.writeBuffer());
        });
    }

    createWorksheet(name, columns, rows, columnsCurrency=[]) {
        return new Promise(() => {
            const worksheet = this.workbook.addWorksheet(name, {
                properties: {tabColor: {argb: 'FFC0000'}}
            });

            worksheet.addTable({
                name: name,
                ref: 'A1',
                headerRow: true,
                columns: columns,
                rows: rows,
                style: {
                    theme: 'TableStyleLight1',
                    showRowStripes: true
                }
            });

            if (columnsCurrency.length) {
                columns.map((column) => {
                    if (columnsCurrency.find((c) => column.name === c)) {
                        worksheet.getColumn(
                            columns.findIndex((c) => c.name === column.name) + 1
                        ).numFmt = 'R$#,##0.00';
                    }
                });
            }
        });
    }
}

export default SpreadsheetService;
