import JwtService from '../services/Jwt.service';

export default (req, res, next) => {
  const rawToken = req.headers.authorization || req.headers.Authorization;

  if (!rawToken) return res.api.send('Você precisa estar logado', res.api.codes.UNAUTHORIZED);

  const token = rawToken.replace('Bearer ', '');

  const jwtService = new JwtService();

  if (!jwtService.valid(token)) return res.api.send('Token inválido', res.api.codes.UNAUTHORIZED);

  req.tokenPayload = jwtService.decode(token);

  return next();
};
